# 技术栈

## 核心技术
- **编程语言**: Go 1.19
- **Web框架**: Gin (github.com/gin-gonic/gin)
- **ORM框架**: GORM (gorm.io/gorm)
- **数据库**: MySQL, MongoDB
- **缓存**: Redis
- **消息队列**: Kafka
- **文档**: Swagger

## 主要依赖库
- go-micro.dev/v4: 微服务框架
- github.com/gin-gonic/gin: HTTP Web框架
- gorm.io/gorm: ORM数据库操作
- github.com/go-redis/redis/v8: Redis客户端
- github.com/samber/lo: 函数式编程工具
- github.com/shopspring/decimal: 精确小数计算
- go.mongodb.org/mongo-driver: MongoDB驱动
- go.opentelemetry.io: 分布式追踪

## 项目构建与运行

### 构建命令
```bash
# 构建HTTP服务
go build -o app_service ./cmd/http_server/main.go

# 使用Makefile构建
make build
```

### 运行命令
```bash
# 直接运行
./app_service

# 使用Docker运行
docker build -t app_service .
docker run -p 8001:8001 app_service
```

### 代码生成
```bash
# 生成数据库模型
go run ./gen/gen.go

# 生成Swagger文档
swag init -g cmd/http_server/main.go -o docs
```

### 环境配置
- 开发环境: conf/http_server/config-dev.yaml
- 测试环境: conf/http_server/config-sit.yaml
- 生产环境: conf/http_server/config-prod.yaml

通过环境变量`ENV`指定运行环境，默认为`dev`。
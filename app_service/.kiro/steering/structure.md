# 项目结构

## 目录组织

```
.
├── apps/                           # 业务模块目录
│   ├── business/                   # 业务相关模块
│   │   ├── announcement/           # 公告系统
│   │   ├── notification/           # 通知系统
│   │   ├── bonus_mall/             # 积分商城
│   │   ├── invite_reward/          # 邀请奖励
│   │   ├── market_changes/         # 市场变动
│   │   ├── story/                  # 故事系统
│   │   ├── synthesis/              # 合成系统
│   │   ├── operation_announcement/ # 运营公告
│   │   └── yc/                     # 其他业务
│   └── platform/                   # 平台基础模块
│       ├── asset/                  # 资产服务
│       ├── common/                 # 公共服务
│       ├── issue/                  # 发行服务
│       ├── system/                 # 系统服务
│       └── user/                   # 用户服务
├── cmd/                            # 应用入口
│   ├── http_server/                # HTTP服务入口
│   └── task_server/                # 任务服务入口
├── conf/                           # 配置文件
├── docs/                           # API文档
├── gen/                            # 代码生成工具
├── global/                         # 全局变量和常量
├── pkg/                            # 公共包
│   ├── middlewares/                # 中间件
│   ├── pagination/                 # 分页工具
│   ├── search/                     # 搜索工具
│   └── util/                       # 工具函数
└── third_party/                    # 第三方服务集成
```

## 模块结构

每个业务模块通常包含以下子目录：

```
module/
├── api/                # API层，处理HTTP请求
│   ├── admin/          # 管理端API
│   ├── open/           # 开放API
│   └── web/            # Web端API
├── dal/                # 数据访问层
│   ├── model/          # 数据模型
│   └── query/          # 查询构造器
├── define/             # 定义层
│   ├── enums/          # 枚举常量
│   └── err_code.go     # 错误码
├── repo/               # 仓储层，数据库操作
├── router/             # 路由定义
├── service/            # 业务逻辑层
│   └── logic/          # 核心业务逻辑
├── consume/            # 消息消费者(可选)
└── facade/             # 对外暴露的接口(可选)
```

## 命名规范

1. **文件命名**: 使用小写字母和下划线，如 `user_service.go`
2. **接口命名**: 使用大驼峰命名法，如 `UserService`
3. **结构体命名**: 使用大驼峰命名法，如 `UserInfo`
4. **方法命名**: 使用大驼峰命名法，如 `GetUserInfo`
5. **变量命名**: 使用小驼峰命名法，如 `userID`
6. **常量命名**: 使用全大写和下划线，如 `MAX_RETRY_COUNT`
7. **错误码规范**: 

## 代码分层

- **API层**: 处理HTTP请求，参数校验，返回响应
- **Service层**: 实现业务逻辑，调用Repo层
- **Repo层**: 封装数据库操作，提供CRUD接口
- **Model层**: 定义数据库表结构
- **Define层**: 定义请求/响应结构体，错误码等
# 实现计划

- [x] 1. 搭建卡牌集社模块基础结构
  - 创建apps/business/card_community目录结构
  - 建立标准的模块目录：api/, dal/, define/, repo/, router/, service/
  - 创建各子目录：api/admin/, api/web/, define/enums/, service/logic/, router/admin/, router/web/
  - _需求: 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1_

- [x] 2. 数据库集成配置
- [x] 2.1 创建MySQL表结构初始化
  - 在gen/SQL/目录创建card_community_init.sql
  - 定义posts、conversations、smart_reply_templates、merchant_applications表结构
  - _需求: 1.2, 2.1, 3.1, 6.1, 7.1_

- [X] 2.2 创建GORM代码生成配置
  - 在gen/目录创建card_community相关代码生成配置
  - 配置dal/model和dal/query的自动生成
  - _需求: 1.2, 2.1, 3.1, 6.1, 7.1_

- [X] 2.3 运行代码生成
  - 执行`go run ./gen/gen.go`生成数据模型和查询代码
  - 生成dal/model和dal/query目录下的代码
  - 生成repo目录下的基础仓储代码
  - _需求: 1.2, 2.1, 3.1, 6.1, 7.1_

- [x] 3. 定义数据模型和枚举类型
- [x] 3.1 创建枚举定义文件
  - 实现define/enums/post_status.go定义帖子状态枚举
  - 实现define/enums/message_type.go定义消息类型枚举
  - 实现define/enums/sender_type.go定义发送者类型枚举
  - 实现define/enums/media_type.go定义媒体类型枚举
  - 实现define/enums/application_status.go定义申请状态枚举
  - _需求: 1.5, 2.2, 5.1, 5.2, 7.1_

- [x] 3.2 补充数据模型结构体
  - 检查并补充dal/model/post.go帖子数据模型
  - 检查并补充dal/model/conversation.go会话数据模型
  - 实现dal/model/message.go定义消息数据模型
  - 检查并补充dal/model/smart_reply_template.go智能回复模板模型
  - 检查并补充dal/model/merchant_application.go商家申请模型
  - _需求: 1.2, 1.3, 2.1, 3.1, 5.1, 6.1, 7.1_

- [x] 4. 创建错误码定义
  - 实现define/err_code.go定义卡牌集社模块专用错误码
  - 包含帖子、会话、消息、智能回复、管理端相关错误码
  - _需求: 1.1-7.2_

- [x] 5. 创建请求响应结构定义
- [x] 5.1 定义Web端请求响应结构
  - 实现define/post_web.go定义帖子相关请求响应结构
  - 实现define/conversation_web.go定义会话相关请求响应结构
  - 实现define/message_web.go定义消息相关请求响应结构
  - 实现define/smart_reply_web.go定义智能回复相关请求响应结构
  - _需求: 1.1-1.6, 2.1-2.6, 3.1-3.7, 4.1-4.6, 5.1-5.6, 6.1-6.7_

- [x] 5.2 定义管理端请求响应结构
  - 实现define/post_admin.go定义管理端帖子管理结构
  - 实现define/conversation_admin.go定义管理端会话管理结构
  - 实现define/merchant_admin.go定义管理端商家管理结构
  - _需求: 7.1, 7.2_

- [x] 6. 实现API层代码
- [x] 6.1 实现Web端API接口
- [x] 6.1.1 实现帖子相关API
  - 实现api/web/post_api.go提供帖子发布、查询、管理接口
  - POST /web/v1/posts/add - 创建帖子
  - GET /web/v1/posts/list - 获取帖子列表
  - GET /web/v1/posts/detail - 获取帖子详情
  - GET /web/v1/posts/my - 获取我的帖子
  - POST /web/v1/posts/edit - 更新帖子状态
  - POST /web/v1/posts/delete - 删除帖子
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 6.1, 6.2, 6.3, 6.4, 6.5, 6.6_

- [x] 6.1.2 实现会话相关API
  - 实现api/web/conversation_api.go提供会话管理接口
  - POST /web/v1/conversations/add - 创建会话
  - GET /web/v1/conversations/list - 获取会话列表
  - GET /web/v1/conversations/detail - 获取会话详情
  - _需求: 2.2, 2.3, 4.1, 4.2, 4.3, 4.4, 4.6_

- [x] 6.1.3 实现消息相关API
  - 实现api/web/message_api.go提供消息收发接口
  - POST /web/v1/conversations/messages/add - 发送消息
  - GET /web/v1/conversations/messages/list - 获取消息列表
  - _需求: 2.5, 2.6, 4.5, 5.1, 5.2, 5.3, 5.4, 5.5, 5.6_

- [x] 6.1.4 实现智能回复相关API
  - 实现api/web/smart_reply_api.go提供智能回复管理接口
  - GET /web/v1/smart_reply_template/detail - 获取智能回复模板
  - POST /web/v1/smart_reply_template - 更新智能回复模板
  - POST /web/v1/smart_reply_template/toggle - 切换智能回复开关
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6_

- [x] 6.1.5 实现商家申请相关API
  - 实现api/web/merchant_application_api.go提供商家申请接口
  - POST /web/v1/merchant_application/add - 提交商家申请
  - GET /web/v1/merchant_application/status - 获取申请状态
  - _需求: 7.1_

- [X] 6.2 实现管理端API代码
- [x] 6.2.1 实现管理端帖子管理API
  - 实现api/admin/post_api.go提供管理端帖子管理
  - GET /admin/v1/posts/list - 获取求购帖子列表
  - GET /admin/v1/posts/detail - 获取求购帖子详情
  - POST /admin/v1/posts/edit - 更新帖子状态（违规下架）
  - _需求: 7.1, 7.2_

- [x] 6.2.2 实现管理端会话管理API
  - 实现api/admin/conversation_api.go提供会话监控
  - GET /admin/v1/conversations/list - 获取会话列表
  - GET /admin/v1/conversations/detail - 获取会话详情
  - _需求: 7.1_

- [x] 6.2.3 实现管理端商家管理API
  - 实现api/admin/merchant_api.go提供商家审核管理
  - GET /admin/v1/merchant_applications/list - 获取商家申请列表
  - GET /admin/v1/merchant_applications/detail - 获取申请详情
  - POST /admin/v1/merchant_applications/review - 审核商家申请
  - _需求: 7.1_



- [x] 7. 生成Swagger文档
  - 为所有API接口添加Swagger注释
  - 运行swag init生成Swagger文档
  - 确保文档正确描述所有接口参数和响应
  - _需求: 1.1-7.2_

- [x] 8. 配置路由注册
- [x] 8.1 创建Web端路由配置
  - 实现router/web/post.go配置帖子相关路由
  - 实现router/web/conversation.go配置会话相关路由
  - 实现router/web/message.go配置消息相关路由
  - 实现router/web/smart_reply.go配置智能回复路由
  - 实现router/web/merchant_application.go配置商家申请路由
  - _需求: 1.1-1.6, 2.1-2.6, 3.1-3.7, 4.1-4.6, 5.1-5.6, 6.1-6.7_

- [x] 8.2 创建管理端路由配置
  - 实现router/admin/post.go配置管理端帖子路由
  - 实现router/admin/conversation.go配置管理端会话路由
  - 实现router/admin/merchant.go配置管理端商家路由
  - _需求: 7.1, 7.2_

- [x] 8.3 创建主路由注册文件
  - 实现router/router.go统一注册所有路由
  - 集成Web端和管理端路由组
  - _需求: 1.1-7.2_

- [x] 9. 实现服务层统一接口
- [x] 9.1 创建服务层接口定义
  - 实现service/post_service.go定义帖子服务接口
  - 实现service/conversation_service.go定义会话服务接口
  - 实现service/message_service.go定义消息服务接口
  - 实现service/smart_reply_service.go定义智能回复服务接口
  - 实现service/admin_service.go定义管理端服务接口
  - _需求: 1.1-7.2_

- [x] 9.2 创建服务层统一入口
  - 实现service/service.go提供服务层统一初始化和依赖注入
  - 集成所有业务逻辑服务
  - _需求: 1.1-7.2_

- [x] 10. 实现核心业务逻辑层
- [x] 10.1 实现帖子管理业务逻辑
  - 实现service/logic/post_logic.go处理帖子创建、查询、状态更新逻辑
  - 实现帖子列表分页查询
  - 实现我的发布帖子查询
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 6.1, 6.2, 6.5, 6.6_

- [x] 10.2 实现会话管理业务逻辑
  - 实现service/logic/conversation_logic.go处理会话创建和管理
  - 支持创建或获取用户与商家的会话
  - 实现会话列表查询和未读消息计数
  - _需求: 2.2, 2.3, 4.1, 4.2, 4.3, 4.6_

- [x] 10.3 实现消息管理业务逻辑
  - 实现service/logic/message_logic.go处理消息发送和接收
  - 支持消息发送
  - 实现消息历史记录分页查询
  - 实现最后阅读时间更新
  - _需求: 2.4, 2.6, 4.4, 4.5, 5.1, 5.2, 5.5_

- [x] 10.4 实现智能回复管理业务逻辑
  - 实现service/logic/smart_reply_logic.go处理智能回复模板管理
  - 支持模板内容的创建和更新
  - 实现智能回复开关切换
  - 支持新会话自动发送智能回复
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7_

- [x] 11. 实现数据访问层
- [x] 11.1 补充MySQL数据访问接口
  - 补充repo/post_repo.go提供帖子CRUD操作
  - 补充repo/conversation_repo.go提供会话CRUD操作
  - 补充repo/smart_reply_template_repo.go提供智能回复模板操作
  - 补充repo/merchant_application_repo.go提供商家申请操作
  - _需求: 1.5, 2.2, 3.4, 3.5, 6.5, 6.6, 7.1_

- [x] 12. 集成到主应用
- [x] 12.1 注册模块路由到主应用
  - 修改主路由文件，添加card_community路由组
  - 配置/card-community路径前缀
  - _需求: 1.1-7.2_

- [] 12.2 配置数据库自动迁移
  - 在主应用启动时添加card_community相关表的AutoMigrate
  - 确保数据库表结构正确创建
  - _需求: 1.2, 2.1, 3.1, 6.1, 7.1_

- [] 13. 创建基础测试用例
- [] 13.1 创建单元测试
  - 为核心业务逻辑创建单元测试文件
  - 测试帖子创建、会话管理、消息发送等核心功能
  - _需求: 1.1-7.2_

- [] 13.2 创建API集成测试
  - 创建API接口的集成测试
  - 测试完整的请求响应流程
  - _需求: 1.1-7.2_
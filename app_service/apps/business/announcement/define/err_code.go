package define

import "e.coding.net/g-dtay0385/common/go-util/response"

var (
	AH200001Err = response.NewError(200001, "已下架不可修改")
	AH200002Err = response.NewError(200002, "仅限定时中和已发布的状态的可操作下架")
	AH200003Err = response.NewError(200003, "有关联公告的栏目不可删除")
	AH200004Err = response.NewError(200004, "仅已发布状态的公告可同步媒体")
	AH200005Err = response.NewError(200005, "不可修改为该状态")
	AH200006Err = response.NewError(200006, "公告已下架")
	AH200007Err = response.NewError(200007, "仅限待发布的状态的可操作发布")
)

package define

import (
	"time"

	"app_service/apps/business/announcement/define/enums"
	"app_service/pkg/pagination"
)

// 公告列表相关结构体
type (
	GetAnnouncementAdminListReq struct {
		pagination.Pagination
		ID         int64                      `form:"id" json:"id,string"`                   // 公告ID
		Title      string                     `form:"title" json:"title"`                    // 公告标题
		CategoryID int64                      `form:"category_id" json:"category_id,string"` // 分类ID
		Status     enums.AnnouncementStatus   `form:"status" json:"status"`                  // 状态
		TimeType   enums.AnnouncementTimeType `form:"time_type" json:"time_type"`            // 时间类型
		StartTime  time.Time                  `form:"start_time" json:"start_time"`          // 开始时间
		EndTime    time.Time                  `form:"end_time" json:"end_time"`              // 结束时间
		ItemId     string                     `form:"item_id" json:"item_id"`                // 商品ID
		ItemName   string                     `form:"item_name" json:"item_name"`            // 商品名称
		ChannelId  string                     `form:"channel_id" json:"channel_id"`          // 渠道ID
		CreatedBy  string                     `form:"created_by" json:"created_by"`          // 创建人
	}

	GetItemIdInfoList struct {
		ID       string `json:"id"`        // ID
		ItemId   string `json:"item_id"`   // 商品ID
		ItemName string `json:"item_name"` // 商品名称
		ImageUrl string `json:"image_url"` // 商品图片
	}

	GetAnnouncementAdminListData struct {
		ID             int64                              `json:"id,string"`          // 公告ID
		Title          string                             `json:"title"`              // 公告标题
		Content        string                             `json:"content"`            // 公告内容
		CategoryID     int64                              `json:"category_id,string"` // 分类ID
		CategoryInfo   *GetAnnCategoryAdminLessDetailResp `json:"category_info"`      // 分类信息
		Priority       int32                              `json:"priority"`           // 优先级
		Status         enums.AnnouncementStatus           `json:"status"`             // 状态
		PublishTime    *time.Time                         `json:"publish_time"`       // 发布时间
		ItemIds        []string                           `json:"item_ids"`           // 关联商品ID列表
		ItemIdInfoList []*GetItemIdInfoList               `json:"item_id_info_list"`  // 关联商品列表
		ChannelIds     []string                           `json:"channel_ids"`        // 关联渠道ID列表
		PublishType    enums.AnnouncementPublishType      `json:"publish_type"`       // 发布类型
		CreatedBy      string                             `json:"created_by"`         // 创建人
		//CreatorInfo   *GetUserInfoAdminResp             `json:"creator_info"`       // 创建人信息
		AdIds     []string  `json:"ad_ids"`     // 关联广告ID列表
		CreatedAt time.Time `json:"created_at"` // 创建时间
		UpdatedAt time.Time `json:"updated_at"` // 更新时间
	}

	GetAnnouncementAdminListResp struct {
		List  []*GetAnnouncementAdminListData `json:"list"`
		Total int64                           `json:"total"`
	}
)

// 公告详情相关结构体
type (
	GetAnnouncementAdminDetailReq struct {
		ID int64 `form:"id" json:"id,string" binding:"required"` // 公告ID
	}

	GetAnnouncementAdminDetailResp struct {
		ID             int64                              `json:"id,string"`          // 公告ID
		Title          string                             `json:"title"`              // 公告标题
		Content        string                             `json:"content"`            // 公告内容
		CategoryID     int64                              `json:"category_id,string"` // 分类ID
		CategoryInfo   *GetAnnCategoryAdminLessDetailResp `json:"category_info"`      // 分类信息
		Priority       int32                              `json:"priority"`           // 优先级
		Status         enums.AnnouncementStatus           `json:"status"`             // 状态
		PublishTime    *time.Time                         `json:"publish_time"`       // 发布时间
		ItemIds        []string                           `json:"item_ids"`           // 关联商品ID列表
		ItemIdInfoList []*GetItemIdInfoList               `json:"item_id_info_list"`  // 关联商品列表
		ChannelIds     []string                           `json:"channel_ids"`        // 关联渠道ID列表
		PublishType    enums.AnnouncementPublishType      `json:"publish_type"`       // 发布类型
		MessagePush    enums.AnnouncementMessagePushEnum  `json:"message_push"`       //消息推送【1:推送, 2:不推送】
		CreatedBy      string                             `json:"created_by"`         // 创建人
		CreatedAt      time.Time                          `json:"created_at"`         // 创建时间
		UpdatedAt      time.Time                          `json:"updated_at"`         // 更新时间
	}
)

// 新增公告相关结构体
type (
	AddAnnouncementReq struct {
		Title       string                            `json:"title" binding:"required"`                  // 公告标题
		Content     string                            `json:"content" binding:"required"`                // 公告内容
		CategoryID  int64                             `json:"category_id,string" binding:"required"`     // 分类ID
		Priority    int32                             `json:"priority"`                                  // 优先级
		PublishTime *time.Time                        `json:"publish_time"`                              // 发布时间
		ItemIds     []string                          `json:"item_ids"`                                  // 关联商品ID列表
		ChannelIds  []string                          `json:"channel_ids"  binding:"required"`           // 关联渠道ID列表
		PublishType enums.AnnouncementPublishType     `json:"publish_type" binding:"required"`           // 发布类型
		MessagePush enums.AnnouncementMessagePushEnum `json:"message_push" binding:"required,oneof=1 2"` //消息推送【1:推送, 2:不推送】
	}

	AddAnnouncementResp struct {
		ID int64 `json:"id"` // 公告ID
	}
)

// 编辑公告相关结构体
type (
	EditAnnouncementReq struct {
		ID          int64                             `json:"id,string" binding:"required"`              // 公告ID
		Title       string                            `json:"title" binding:"required"`                  // 公告标题
		Content     string                            `json:"content" binding:"required"`                // 公告内容
		CategoryID  int64                             `json:"category_id,string" binding:"required"`     // 分类ID
		Priority    int32                             `json:"priority"`                                  // 优先级
		PublishTime *time.Time                        `json:"publish_time"`                              // 发布时间
		ItemIds     []string                          `json:"item_ids"`                                  // 关联商品ID列表
		ChannelIds  []string                          `json:"channel_ids"  binding:"required"`           // 关联渠道ID列表
		PublishType enums.AnnouncementPublishType     `json:"publish_type" binding:"required"`           // 发布类型
		MessagePush enums.AnnouncementMessagePushEnum `json:"message_push" binding:"required,oneof=1 2"` //消息推送【1:推送, 2:不推送】
	}

	EditAnnouncementResp struct {
		ID int64 `json:"id,string"` // 公告ID
	}
)

// 编辑公告状态相关结构体
type (
	EditAnnouncementStatusReq struct {
		ID     int64                    `json:"id,string" binding:"required"` // 公告ID
		Status enums.AnnouncementStatus `json:"status" binding:"required"`    // 状态
	}

	EditAnnouncementStatusResp struct {
		ID int64 `json:"id,string"` // 公告ID
	}
)

// 编辑公告优先级相关结构体
type (
	EditAnnouncementPriorityReq struct {
		ID       int64 `json:"id,string" binding:"required"` // 公告ID
		Priority int32 `json:"priority" binding:"required"`  // 优先级
	}

	EditAnnouncementPriorityResp struct {
		ID int64 `json:"id,string"` // 公告ID
	}
)

// 编辑公告关联广告ID相关结构体
type (
	EditAnnouncementAdIdsReq struct {
		ID    int64    `json:"id,string" binding:"required"` // 公告ID
		AdIds []string `json:"ad_ids" binding:"required"`    // 关联广告ID列表
	}

	EditAnnouncementAdIdsResp struct {
		ID int64 `json:"id,string"` // 公告ID
	}
)

// 删除公告相关结构体
type (
	DelAnnouncementReq struct {
		ID int64 `json:"id,string" binding:"required"` // 公告ID
	}

	DelAnnouncementResp struct {
		ID int64 `json:"id,string"` // 公告ID
	}
)

// 公告分类列表相关结构体
type (
	GetAnnCategoryAdminListReq struct {
		pagination.Pagination
		//Name   string `form:"name" json:"name"`     // 分类名称
		//Status int32  `form:"status" json:"status"` // 状态
	}

	GetAnnCategoryAdminListData struct {
		ID        int64                   `json:"id,string"`  // 分类ID
		Name      string                  `json:"name"`       // 分类名称
		AnnNum    int32                   `json:"ann_num"`    // 公告数量
		Priority  int32                   `json:"priority"`   // 优先级
		Status    enums.AnnCategoryStatus `json:"status"`     // 状态
		CreatedAt time.Time               `json:"created_at"` // 创建时间
		UpdatedAt time.Time               `json:"updated_at"` // 更新时间
	}

	GetAnnCategoryAdminListResp struct {
		List  []*GetAnnCategoryAdminListData `json:"list"`
		Total int64                          `json:"total"`
	}
)

// 公告分类详情相关结构体
type (
	GetAnnCategoryAdminDetailReq struct {
		ID int64 `form:"id" json:"id,string" binding:"required"` // 分类ID
	}

	GetAnnCategoryAdminDetailResp struct {
		ID              int64                   `json:"id,string"`        // 分类ID
		Name            string                  `json:"name"`             // 分类名称
		TextColor       string                  `json:"text_color"`       // 文字颜色
		BackgroundColor string                  `json:"background_color"` // 背景颜色
		Priority        int32                   `json:"priority"`         // 优先级
		Status          enums.AnnCategoryStatus `json:"status"`           // 状态
		CreatedAt       time.Time               `json:"created_at"`       // 创建时间
		UpdatedAt       time.Time               `json:"updated_at"`       // 更新时间
	}

	GetAnnCategoryAdminLessDetailResp struct {
		ID   int64  `json:"id,string"` // 分类ID
		Name string `json:"name"`      // 分类名称
	}
)

// 新增公告分类相关结构体
type (
	AddAnnCategoryReq struct {
		Name            string `json:"name" binding:"required"`             // 分类名称
		TextColor       string `json:"text_color" binding:"required"`       // 文字颜色
		BackgroundColor string `json:"background_color" binding:"required"` // 背景颜色
		Priority        int32  `json:"priority" binding:"required"`         // 优先级
	}

	AddAnnCategoryResp struct {
		ID int64 `json:"id,string"` // 分类ID
	}
)

// 编辑公告分类相关结构体
type (
	EditAnnCategoryReq struct {
		ID              int64  `json:"id,string" binding:"required"`        // 分类ID
		Name            string `json:"name" binding:"required"`             // 分类名称
		TextColor       string `json:"text_color" binding:"required"`       // 文字颜色
		BackgroundColor string `json:"background_color" binding:"required"` // 背景颜色
		Priority        int32  `json:"priority" binding:"required"`         // 优先级
	}

	EditAnnCategoryResp struct {
		ID int64 `json:"id,string"` // 分类ID
	}
)

// 编辑公告分类优先级相关结构体
type (
	EditAnnCategoryPriorityReq struct {
		ID       int64 `json:"id,string" binding:"required"` // 分类ID
		Priority int32 `json:"priority" binding:"required"`  //  优先级
	}

	EditAnnCategoryPriorityResp struct {
		ID int64 `json:"id,string"` // 分类ID
	}
)

// 删除公告分类相关结构体
type (
	DelAnnCategoryReq struct {
		ID int64 `json:"id,string" binding:"required"` // 分类ID
	}

	DelAnnCategoryResp struct {
		ID int64 `json:"id,string"` // 分类ID
	}
)

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
)

const TableNameAnnouncement = "announcement"

// Announcement 公告表
type Announcement struct {
	AnnID        int64           `gorm:"column:ann_id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键ID" json:"ann_id"`       // 主键ID
	Title        string          `gorm:"column:title;type:varchar(255);not null;comment:公告标题" json:"title"`                                 // 公告标题
	Content      string          `gorm:"column:content;type:text;not null;comment:公告内容" json:"content"`                                     // 公告内容
	CategoryID   int64           `gorm:"column:category_id;type:bigint unsigned;not null;comment:栏目ID" json:"category_id"`                  // 栏目ID
	Priority     int32           `gorm:"column:priority;type:int unsigned;comment:优先级" json:"priority"`                                     // 优先级
	Status       int32           `gorm:"column:status;type:tinyint;not null;default:1;comment:状态：1待发布 2定时中 3已发布 4已下架" json:"status"`        // 状态：1待发布 2定时中 3已发布 4已下架
	PublishTime  *time.Time      `gorm:"column:publish_time;type:datetime;comment:发布时间" json:"publish_time"`                                // 发布时间
	ItemIds      *datatypes.JSON `gorm:"column:item_ids;type:json;comment:关联商品挂牌编码" json:"item_ids"`                                        // 关联商品挂牌编码
	ChannelIds   datatypes.JSON  `gorm:"column:channel_ids;type:json;not null;comment:发布终端列表" json:"channel_ids"`                           // 发布终端列表
	PublishType  int32           `gorm:"column:publish_type;type:tinyint;not null;default:1;comment:发布方式：1立即发布 2定时发布" json:"publish_type"`  // 发布方式：1立即发布 2定时发布
	MessagePush  int32           `gorm:"column:message_push;type:tinyint;comment:消息推送【1:推送, 2:不推送】" json:"message_push"`                    // 消息推送【1:推送, 2:不推送】
	CreatedBy    string          `gorm:"column:created_by;type:char(24);not null;comment:创建人" json:"created_by"`                            // 创建人
	AdIds        *datatypes.JSON `gorm:"column:ad_ids;type:json;comment:广告商ids，关联tmt-ad_id" json:"ad_ids"`                                  // 广告商ids，关联tmt-ad_id
	CreatedAt    time.Time       `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt    time.Time       `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
	CategoryInfo AnnCategory     `gorm:"foreignKey:category_id;references:ann_category_id" json:"category_info"`
}

// TableName Announcement's table name
func (*Announcement) TableName() string {
	return TableNameAnnouncement
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameAnnCategory = "ann_category"

// AnnCategory 公告栏目表
type AnnCategory struct {
	AnnCategoryID   int64                 `gorm:"column:ann_category_id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键ID" json:"ann_category_id"` // 主键ID
	Name            string                `gorm:"column:name;type:varchar(50);not null;comment:栏目名称" json:"name"`                                                // 栏目名称
	TextColor       string                `gorm:"column:text_color;type:char(7);not null;comment:文字颜色" json:"text_color"`                                        // 文字颜色
	BackgroundColor string                `gorm:"column:background_color;type:char(7);not null;comment:背景颜色" json:"background_color"`                            // 背景颜色
	Priority        int32                 `gorm:"column:priority;type:int unsigned;not null;comment:优先级，最大值9999" json:"priority"`                                // 优先级，最大值9999
	Status          int32                 `gorm:"column:status;type:tinyint;not null;default:1;comment:状态：-1:已删除 1启用" json:"status"`                             // 状态：-1:已删除 1启用
	CreatedAt       time.Time             `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`             // 创建时间
	UpdatedAt       time.Time             `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`             // 更新时间
	IsDel           soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`              // 是否删除【0->未删除; 1->删除】
}

// TableName AnnCategory's table name
func (*AnnCategory) TableName() string {
	return TableNameAnnCategory
}

package service

import (
	"app_service/apps/business/announcement/dal/model"
	"app_service/apps/business/announcement/define"
	"app_service/apps/business/announcement/define/enums"
	"app_service/apps/business/announcement/repo"
	"app_service/apps/business/announcement/service/logic"
	opermodel "app_service/apps/business/operation_announcement/dal/model"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/third_party/pbs"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"fmt"
	"sort"
	"time"
)

// SchedulePublish 定时发布公告
func (s *Service) SchedulePublish(req *define.SchedulePublishReq) (*define.SchedulePublishResp, error) {
	var annList []*model.Announcement
	err := repo.GetDB().WithContext(s.ctx).
		Where("status = ?", enums.AnnouncementStatusScheduled.Val()).
		Where("publish_time < ?", util.Now()).
		Find(&annList).Error
	if err != nil {
		log.Ctx(s.ctx).Errorf("定时发布平台方公告失败: %v", err)
		return nil, err
	}
	annIDs := make([]int64, len(annList))
	for idx, ann := range annList {
		annIDs[idx] = ann.AnnID
	}
	if len(annIDs) > 0 {
		// 更新状态为【已发布】
		updateParams := map[string]interface{}{
			"status": enums.AnnouncementStatusPublished.Val(),
		}
		annSchema := repo.GetQuery().Announcement
		updateQb := search.NewQueryBuilder().In(annSchema.AnnID, annIDs).Build()
		err = repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx)).UpdateField(updateParams, updateQb)
		if err != nil {
			log.Ctx(s.ctx).Errorf("定时发布平台方公告失败: %v", err)
			return nil, err
		}

		// 消息推送
		spanCtx := s.NewContextWithSpanContext(s.ctx)
		go func() {
			for _, ann := range annList {
				if ann.MessagePush == enums.AnnouncementMessagePushYes.Val() {
					pushErr := logic.PushAnnouncementMessage(spanCtx, ann)
					if pushErr != nil {
						log.Ctx(spanCtx).Errorf("平台方公告消息推送失败: %v", pushErr)
					}

					time.Sleep(time.Second * 3)
				}
			}
		}()
	}

	return &define.SchedulePublishResp{}, nil
}

// NoticeAggregate 聚合公告和活动
func (s *Service) NoticeAggregate(req *define.NoticeAggregateReq) ([]*define.UnifiedNotice, error) {
	ctx := s.ctx
	adID := req.AdID
	db := repo.GetDB().WithContext(s.ctx)
	// 1. 查询平台方公告
	var anns []*model.Announcement
	if err := db.Where("status = 3 AND JSON_CONTAINS(ad_ids, ?)", "\""+adID+"\"").
		Order("created_at desc").Find(&anns).Error; err != nil {
		return nil, err
	}

	// 查询运营方公告
	var operAnns []*opermodel.OperationAnnouncement
	if err := db.Where("status = 3 AND JSON_CONTAINS(ad_ids, ?)", "\""+adID+"\"").
		Order("created_at desc").Find(&operAnns).Error; err != nil {
		return nil, err
	}

	// 2. 查询活动
	actList, err := pbs.QueryActWithAdData(ctx, adID)
	if err != nil {
		// 记录日志，降级只返回公告
		log.Ctx(ctx).Errorf("QueryActWithAdData err:%v", err)
		actList = nil
	}

	// 3. 组装统一结构体
	notices := make([]*define.UnifiedNotice, 0, len(anns)+len(actList)+len(operAnns))
	// 平台方公告
	h5Domain := "https://sit-wcjs.ahbq.com.cn"
	if global.GlobalConfig.Service.Env == global.EnvProd {
		h5Domain = "https://wcjs.ahbq.com.cn"
	}
	for _, ann := range anns {
		notices = append(notices, &define.UnifiedNotice{
			ID:       "ann_" + fmt.Sprintf("%d", ann.AnnID),
			Title:    ann.Title,
			Link:     fmt.Sprintf("%s/pages/MessageDetail/index?id=%d", h5Domain, ann.AnnID),
			SortTime: ann.PublishTime.UTC(),
		})
	}
	// 运营方公告
	for _, ann := range operAnns {
		notices = append(notices, &define.UnifiedNotice{
			ID:       "oper_ann_" + fmt.Sprintf("%d", ann.OperationAnnouncementID),
			Title:    ann.Title,
			Link:     fmt.Sprintf("%s/pages/MessageDetail/index?id=%d&type=operation", h5Domain, ann.OperationAnnouncementID),
			SortTime: ann.PublishTime.UTC(),
		})
	}
	for _, act := range actList {
		notices = append(notices, &define.UnifiedNotice{
			ID:       "act_" + fmt.Sprintf("%d", act.ID),
			Title:    act.Title,
			Link:     act.Link,
			SortTime: act.BeginTime,
		})
	}

	// 4. 按 CreatedAt 倒序排序
	sort.Slice(notices, func(i, j int) bool {
		return notices[i].SortTime.After(notices[j].SortTime)
	})

	return notices, nil
}

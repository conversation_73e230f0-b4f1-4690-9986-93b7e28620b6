package service

import (
	"app_service/apps/business/announcement/dal/model"
	"app_service/apps/business/announcement/define"
	"app_service/apps/business/announcement/define/enums"
	"app_service/apps/business/announcement/repo"
	"app_service/apps/business/announcement/service/logic"
	"app_service/pkg/search"
	"fmt"
	"strings"
	"time"
)

// GetAnnouncementWebList 公告列表查询，仅查已发布公告，按 web 端结构组装响应。
func (s *Service) GetAnnouncementWebList(req *define.GetAnnouncementWebListReq) (*define.GetAnnouncementWebListResp, error) {
	db := repo.GetDB().WithContext(s.ctx)

	// 使用公共条件
	where, args := logic.GetPublishedAndChannelCondition(s.ctx)
	db = db.Where(where, args...)

	if req.KeyWord != "" {
		// 搜索标题和内容
		db = db.Where("title LIKE ? OR content LIKE ?", "%"+req.<PERSON><PERSON>ord+"%", "%"+req.KeyWord+"%")
	}
	if req.CategoryID != 0 {
		db = db.Where("category_id = ?", req.CategoryID)
	}
	if req.ItemId != "" {
		db = db.Where("JSON_CONTAINS(item_ids, ?)", "\""+req.ItemId+"\"")
	}

	// 排序、分页
	if req.OrderBy != "" && req.SortOrder != "" {
		// 传了排序字段，按照请求的排序参数排序
		isDesc := strings.ToLower(req.SortOrder) == "desc"
		orderBy := strings.ToLower(req.OrderBy)
		switch orderBy {
		case "publish_time":
			if isDesc {
				db = db.Order("publish_time DESC")
			} else {
				db = db.Order("publish_time ASC")
			}
		}
	} else {
		// 默认排序方式
		db = db.Order("priority DESC, publish_time DESC")
	}
	offset := (req.GetPage() - 1) * req.GetPageSize()
	if offset < 0 {
		offset = 0
	}
	var list []*model.Announcement
	err := db.Offset(offset).Limit(req.GetPageSize()).Find(&list).Error
	if err != nil {
		return nil, err
	}
	resp := &define.GetAnnouncementWebListResp{
		List: make([]*define.GetAnnouncementWebListData, 0),
	}
	if len(list) == 0 {
		return resp, nil
	}

	// 批量查分类
	var categoryIds []int64
	for _, v := range list {
		categoryIds = append(categoryIds, v.CategoryID)
	}
	categorySchema := repo.GetQuery().AnnCategory
	categories, _, err := repo.NewAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectPage(
		search.NewQueryBuilder().In(categorySchema.AnnCategoryID, categoryIds).Build(),
		1, len(categoryIds),
	)
	if err != nil {
		return nil, err
	}
	categoryMap := make(map[int64]*model.AnnCategory)
	for _, category := range categories {
		categoryMap[category.AnnCategoryID] = category
	}

	// 获取当前时间（本地时区）
	now := time.Now()
	formatNow := now.Format("2006-01-02T15:04:05.000Z07:00")

	for _, v := range list {
		var categoryInfo *define.GetAnnCategoryWebLessDetailResp
		if category, ok := categoryMap[v.CategoryID]; ok {
			categoryInfo = &define.GetAnnCategoryWebLessDetailResp{
				ID:              category.AnnCategoryID,
				Name:            category.Name,
				TextColor:       category.TextColor,
				BackgroundColor: category.BackgroundColor,
			}
		}
		resp.List = append(resp.List, &define.GetAnnouncementWebListData{
			ID:           v.AnnID,
			Title:        v.Title,
			CategoryID:   v.CategoryID,
			CategoryInfo: categoryInfo,
			PublishTime:  v.PublishTime,
			CurrentTime:  formatNow,
		})
	}
	resp.HasMore = len(resp.List) == req.GetPageSize()
	return resp, nil
}

// GetAnnouncementWebDetail 公告详情查询，仅查已发布公告。
func (s *Service) GetAnnouncementWebDetail(req *define.GetAnnouncementWebDetailReq) (*define.GetAnnouncementWebDetailResp, error) {
	annSchema := repo.GetQuery().Announcement
	builder := search.NewQueryBuilder().Eq(annSchema.AnnID, req.ID)
	announcement, err := repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}
	if announcement.Status != enums.AnnouncementStatusPublished.Val() {
		return nil, define.AH200006Err
	}

	// 解析 ItemIds
	var itemIds []string
	if announcement.ItemIds != nil {
		itemIds, err = logic.UnmarshalIds(*announcement.ItemIds)
		if err != nil {
			return nil, err
		}
	}

	// 查分类信息
	categorySchema := repo.GetQuery().AnnCategory
	category, err := repo.NewAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectOne(
		search.NewQueryBuilder().Eq(categorySchema.AnnCategoryID, announcement.CategoryID).Build(),
	)
	if err != nil {
		return nil, err
	}
	var categoryInfo *define.GetAnnCategoryWebLessDetailResp
	if category != nil {
		categoryInfo = &define.GetAnnCategoryWebLessDetailResp{
			ID:              category.AnnCategoryID,
			Name:            category.Name,
			TextColor:       category.TextColor,
			BackgroundColor: category.BackgroundColor,
		}
	}

	return &define.GetAnnouncementWebDetailResp{
		ID:           announcement.AnnID,
		Title:        announcement.Title,
		Content:      announcement.Content,
		CategoryID:   announcement.CategoryID,
		CategoryInfo: categoryInfo,
		PublishTime:  announcement.PublishTime,
		ItemIds:      itemIds,
	}, nil
}

// GetAnnCategoryWebList 公告分类列表查询，仅查有效分类。
func (s *Service) GetAnnCategoryWebList(req *define.GetAnnCategoryWebListReq) (*define.GetAnnCategoryWebListResp, error) {
	var categories []model.AnnCategory
	db := repo.GetDB().WithContext(s.ctx)

	// 获取公共条件
	where, args := logic.GetPublishedAndChannelCondition(s.ctx)

	// 构建 EXISTS 子查询
	existsWhere := fmt.Sprintf(`
		EXISTS (
			SELECT 1 FROM announcement
			WHERE announcement.category_id = ann_category.ann_category_id
				AND %s
		)
	`, where)

	err := db.
		Table("ann_category").
		Where("status = ?", enums.AnnCategoryStatusEnable.Val()).
		Where(existsWhere, args...).
		Order("priority DESC, created_at DESC").
		Limit(req.GetPageSize()).
		Offset((req.GetPage() - 1) * req.GetPageSize()).
		Find(&categories).Error

	if err != nil {
		return nil, err
	}
	resp := &define.GetAnnCategoryWebListResp{
		List: make([]*define.GetAnnCategoryWebListData, 0),
	}
	if len(categories) == 0 {
		return resp, nil
	}

	for _, v := range categories {
		resp.List = append(resp.List, &define.GetAnnCategoryWebListData{
			ID:   v.AnnCategoryID,
			Name: v.Name,
		})
	}
	resp.HasMore = len(resp.List) == req.GetPageSize()
	return resp, nil
}

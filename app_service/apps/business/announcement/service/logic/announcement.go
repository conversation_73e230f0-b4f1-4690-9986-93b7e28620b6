package logic

import (
	"app_service/apps/business/announcement/dal/model"
	"app_service/apps/business/announcement/define/enums"
	"app_service/apps/business/announcement/repo"
	notidefine "app_service/apps/business/notification/define"
	notienums "app_service/apps/business/notification/define/enums"
	notifacade "app_service/apps/business/notification/facade"
	"app_service/apps/platform/common/constant"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"encoding/json"
	"fmt"

	"gorm.io/datatypes"
)

// UnmarshalIds 解析ids
func UnmarshalIds(Ids datatypes.JSON) ([]string, error) {
	var data []string
	if err := json.Unmarshal(Ids, &data); err != nil {
		return nil, err
	}
	return data, nil
}

// GetCategoryAnnCounts 获取分类公告数量
// 返回 map[categoryID]count
func GetCategoryAnnCounts(ctx context.Context, categoryIds []int64) (map[int64]int32, error) {
	annSchema := repo.GetQuery().Announcement
	annRepo := repo.NewAnnouncementRepo(annSchema.WithContext(ctx))

	// 构建查询条件
	builder := search.NewQueryBuilder()
	if len(categoryIds) > 0 {
		builder = builder.In(annSchema.CategoryID, categoryIds)
	}

	// 只查询CategoryID字段，提升查询效率
	builder = builder.Select(annSchema.CategoryID)

	// 获取公告列表
	announcements, err := annRepo.SelectList(builder.Build())
	if err != nil {
		return nil, err
	}

	// 统计每个分类的公告数量
	categoryCountMap := make(map[int64]int32)
	for _, ann := range announcements {
		categoryCountMap[ann.CategoryID]++
	}

	return categoryCountMap, nil
}

// GetPublishedAndChannelCondition 获取已发布和渠道过滤条件
func GetPublishedAndChannelCondition(ctx context.Context) (string, []interface{}) {
	channel, _ := ctx.Value(constant.AppChannel).(string)
	where := "status = ? AND (JSON_CONTAINS(channel_ids, ?) OR JSON_CONTAINS(channel_ids, '\"all\"'))"
	args := []interface{}{
		enums.AnnouncementStatusPublished.Val(),
		fmt.Sprintf("\"%s\"", channel),
	}
	return where, args
}

// PushAnnouncementMessage 推送平台方公告消息
func PushAnnouncementMessage(ctx context.Context, ann *model.Announcement) error {
	audienceType := notienums.PushAudienceTypeChannel
	// 解析ChannelIds
	var channelIds []string
	if len(ann.ChannelIds) > 0 {
		chIds, err := UnmarshalIds(ann.ChannelIds)
		if err != nil {
			return err
		}
		channelIds = chIds
	}
	for _, ch := range channelIds {
		if ch == "all" {
			audienceType = notienums.PushAudienceTypeAll
			break
		}
	}
	extras := make(map[string]interface{})
	pageURL := "ojb://message_list?type=0" // 平台方公告列表页
	if ann.Content != "" {
		// 有内容就跳转到详情页
		baseURL := "https://sit-wcjs.ahbq.com.cn"
		if global.GlobalConfig.Service.Env == global.EnvProd {
			baseURL = "https://wcjs.ahbq.com.cn"
		}
		pageURL = fmt.Sprintf("ojb://webview_h5?url=%s/pages/MessageDetail/index?id=%d",
			baseURL, ann.AnnID)
	}
	extras["url"] = pageURL
	message := notidefine.PushMessage{
		Title:         "平台方公告",
		Content:       ann.Title,
		AudienceType:  audienceType,
		ChannelIDs:    channelIds,
		AndroidExtras: extras,
		IOSExtras:     extras,
	}
	relateInfo := notidefine.PushRelateInfo{
		RelateType:  "platform_announcement",
		RelateScene: "publish",
		RelateID:    util.StrVal(ann.AnnID),
	}
	result, err := notifacade.PushMessage(ctx, message, relateInfo)
	if err != nil {
		return err
	}

	log.Ctx(ctx).Infof("平台方公告消息推送成功，ID: %d ,推送总数: %d , 成功: %d , 失败: %d ",
		ann.AnnID, result.SendTotal, result.SuccessCount, result.FailCount)

	return nil
}

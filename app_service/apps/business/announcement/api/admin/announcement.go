package admin

import (
	"app_service/apps/business/announcement/define"
	"app_service/apps/business/announcement/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetAnnouncementList
// @Summary 查询公告列表
// @Description 查询公告列表
// @Tags 管理端-平台方公告管理
// @Param data query define.GetAnnouncementAdminListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetAnnouncementAdminListResp}
// @Router /admin/v1/announcement/list [get]
// @Security Bearer
func GetAnnouncementList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAnnouncementAdminListReq{}, s.GetAnnouncementList)
}

// GetAnnouncementDetail
// @Summary 查询公告详情
// @Description 查询公告详情
// @Tags 管理端-平台方公告管理
// @Param data query define.GetAnnouncementAdminDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetAnnouncementAdminDetailResp}
// @Router /admin/v1/announcement/detail [get]
// @Security Bearer
func GetAnnouncementDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAnnouncementAdminDetailReq{}, s.GetAnnouncementDetail)
}

// AddAnnouncement
// @Summary 新增公告
// @Description 新增公告
// @Tags 管理端-平台方公告管理
// @Param data body define.AddAnnouncementReq true "新增参数"
// @Success 200 {object} response.Data{data=define.AddAnnouncementResp}
// @Router /admin/v1/announcement/add [POST]
// @Security Bearer
func AddAnnouncement(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AddAnnouncementReq{}, s.AddAnnouncement)
}

// EditAnnouncement
// @Summary 编辑公告
// @Description 编辑公告
// @Tags 管理端-平台方公告管理
// @Param data body define.EditAnnouncementReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditAnnouncementResp}
// @Router /admin/v1/announcement/edit [POST]
// @Security Bearer
func EditAnnouncement(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditAnnouncementReq{}, s.EditAnnouncement)
}

// EditAnnouncementStatus
// @Summary 公告状态编辑
// @Description 公告状态编辑
// @Tags 管理端-平台方公告管理
// @Param data body define.EditAnnouncementStatusReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditAnnouncementStatusResp}
// @Router /admin/v1/announcement/edit_status [POST]
// @Security Bearer
func EditAnnouncementStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditAnnouncementStatusReq{}, s.EditAnnouncementStatus)
}

// EditAnnouncementPriority
// @Summary 公告优先级编辑
// @Description 公告优先级编辑
// @Tags 管理端-平台方公告管理
// @Param data body define.EditAnnouncementPriorityReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditAnnouncementPriorityResp}
// @Router /admin/v1/announcement/edit_priority [POST]
// @Security Bearer
func EditAnnouncementPriority(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditAnnouncementPriorityReq{}, s.EditAnnouncementPriority)
}

// EditAnnouncementAdIds
// @Summary 公告关联广告ID编辑
// @Description 公告关联广告ID编辑
// @Tags 管理端-平台方公告管理
// @Param data body define.EditAnnouncementAdIdsReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditAnnouncementAdIdsResp}
// @Router /admin/v1/announcement/edit_ad_ids [POST]
// @Security Bearer
func EditAnnouncementAdIds(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditAnnouncementAdIdsReq{}, s.EditAnnouncementAdIds)
}

// DelAnnouncement
// @Summary 公告删除
// @Description 公告删除
// @Tags 管理端-平台方公告管理
// @Param data body define.DelAnnouncementReq true "删除参数"
// @Success 200 {object} response.Data{data=define.DelAnnouncementResp}
// @Router /admin/v1/announcement/del [POST]
// @Security Bearer
func DelAnnouncement(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.DelAnnouncementReq{}, s.DelAnnouncement)
}

package admin

import (
	"app_service/apps/business/announcement/api/admin"
	"github.com/gin-gonic/gin"
)

// AnnCategory 公告分类管理端相关
func AnnCategory(router *gin.RouterGroup) {
	group := router.Group("/announcement/category")
	{
		// 获取公告分类列表
		group.GET("/list", admin.GetAnnCategoryList)
		//// 获取公告分类详情
		group.GET("/detail", admin.GetAnnCategoryDetail)
		// 新增公告分类
		group.POST("/add", admin.AddAnnCategory)
		// 编辑公告分类
		group.POST("/edit", admin.EditAnnCategory)
		// 公告分类优先级编辑
		group.POST("/edit_priority", admin.EditAnnCategoryPriority)
		// 公告分类删除
		group.POST("/del", admin.DelAnnCategory)
	}
}

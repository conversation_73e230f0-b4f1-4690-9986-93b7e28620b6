package service

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	"context"
	"testing"
)

// TestPushOrderNotification 测试订单推送通知功能
func TestPushOrderNotification(t *testing.T) {
	// 创建服务实例
	service := &Service{
		ctx: context.Background(),
	}

	// 测试订单推送通知
	req := &define.PushOrderRequest{
		UserID:      "test_user_123",
		OrderID:     "test_order_456",
		PushType:    enums.PushTypeOrderUnPaid,
		FromUser:    "卖家昵称",
		OrderStatus: enums.OrderStatusUnPaid,
	}

	// 调用推送方法（注意：这会调用真实的推送服务，在测试环境中可能需要mock）
	err := service.PushOrderNotification(req)
	if err != nil {
		t.Errorf("PushOrderNotification failed: %v", err)
	}

	t.Log("订单推送通知测试完成")
}

// TestPushMessageNotification 测试消息推送通知功能
func TestPushMessageNotification(t *testing.T) {
	// 创建服务实例
	service := &Service{
		ctx: context.Background(),
	}

	// 测试消息推送通知
	req := &define.PushMessageRequest{
		UserID:      "test_user_123",
		MessageID:   "test_message_789",
		MessageType: enums.MessageTypeText,
		Content:     "这是一条测试消息内容",
		FromUser:    "发送者昵称",
	}

	// 调用推送方法
	err := service.PushMessageNotification(req)
	if err != nil {
		t.Errorf("PushMessageNotification failed: %v", err)
	}

	t.Log("消息推送通知测试完成")
}

// TestBuildOrderPushContent 测试订单推送内容构造
func TestBuildOrderPushContent(t *testing.T) {
	service := &Service{
		ctx: context.Background(),
	}

	testCases := []struct {
		name        string
		req         *define.PushOrderRequest
		wantTitle   string
		wantContent string
	}{
		{
			name: "待支付订单推送",
			req: &define.PushOrderRequest{
				PushType: enums.PushTypeOrderUnPaid,
				FromUser: "张三",
			},
			wantTitle:   "你有新的订单「待付款」",
			wantContent: "来自 张三",
		},
		{
			name: "待发货订单推送",
			req: &define.PushOrderRequest{
				PushType: enums.PushTypeOrderUnDelivered,
				FromUser: "李四",
			},
			wantTitle:   "你有订单「待发货」",
			wantContent: "来自 李四",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			title, content := service.buildOrderPushContent(tc.req)
			if title != tc.wantTitle {
				t.Errorf("buildOrderPushContent() title = %v, want %v", title, tc.wantTitle)
			}
			if content != tc.wantContent {
				t.Errorf("buildOrderPushContent() content = %v, want %v", content, tc.wantContent)
			}
		})
	}
}

// TestBuildMessagePushContent 测试消息推送内容构造
func TestBuildMessagePushContent(t *testing.T) {
	service := &Service{
		ctx: context.Background(),
	}

	testCases := []struct {
		name        string
		req         *define.PushMessageRequest
		wantTitle   string
		wantContent string
	}{
		{
			name: "文本消息推送",
			req: &define.PushMessageRequest{
				MessageType: enums.MessageTypeText,
				Content:     "Hello World",
				FromUser:    "王五",
			},
			wantTitle:   "王五给您发送了消息",
			wantContent: "Hello World",
		},
		{
			name: "图片消息推送",
			req: &define.PushMessageRequest{
				MessageType: enums.MessageTypeImage,
				FromUser:    "赵六",
			},
			wantTitle:   "赵六给您发送了消息",
			wantContent: "[图片]",
		},
		{
			name: "长文本消息截断",
			req: &define.PushMessageRequest{
				MessageType: enums.MessageTypeText,
				Content:     "This is a very long message content that exceeds 50 characters and should be truncated with ellipsis for display",
				FromUser:    "测试用户",
			},
			wantTitle:   "测试用户给您发送了消息",
			wantContent: "This is a very long message content that exceeds 5...",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			title, content := service.buildMessagePushContent(tc.req)
			if title != tc.wantTitle {
				t.Errorf("buildMessagePushContent() title = %v, want %v", title, tc.wantTitle)
			}
			if content != tc.wantContent {
				t.Errorf("buildMessagePushContent() content = %v, want %v", content, tc.wantContent)
			}
		})
	}
}

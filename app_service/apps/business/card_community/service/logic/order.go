package logic

import (
	"app_service/apps/business/card_community/define"
	"app_service/third_party/pat"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"strings"
)

// CheckUserRealName 检查用户是否已实名认证
func CheckUserRealName(ctx context.Context, userID string) error {
	if userID == "" {
		return define.CC500701Err.SetMsg("用户ID不能为空")
	}

	realInfos, err := pat.GetRealInfo(ctx, []string{userID})
	if err != nil {
		log.Ctx(ctx).Errorf("获取用户实名信息失败: %v", err)
		return define.CC500701Err.SetMsg("获取用户实名信息失败")
	}

	if len(realInfos) == 0 || realInfos[0].RealName == "" {
		return define.CC500701Err.SetMsg("请先完成实名认证")
	}

	return nil
}

// MaskAddress 脱敏收货地址信息
func MaskAddress(address *define.Address) *define.Address {
	if address == nil {
		return nil
	}

	return &define.Address{
		Name:        MaskReceiverName(address.Name),
		MobilePhone: MaskPhoneNumber(address.MobilePhone),
		Code:        address.Code,
		Area:        MaskArea(address.Area),
		Place:       MaskPlace(address.Place),
	}
}

// MaskReceiverName 脱敏收货人姓名：只显示姓氏
func MaskReceiverName(name string) string {
	if len(name) == 0 {
		return ""
	}
	runes := []rune(name)
	if len(runes) == 1 {
		return string(runes[0]) + "*"
	}
	return string(runes[0]) + "*"
}

// MaskPhoneNumber 脱敏手机号：133****1234
func MaskPhoneNumber(phone string) string {
	if len(phone) != 11 {
		return phone
	}
	return phone[:3] + "****" + phone[7:]
}

// MaskArea 脱敏区域信息：只保留到"区"
func MaskArea(area string) string {
	if area == "" {
		return ""
	}
	// 查找"区"字的位置
	if idx := strings.Index(area, "区"); idx != -1 {
		return area[:idx+len("区")]
	}
	// 如果没有"区"字，返回前几个字符
	runes := []rune(area)
	if len(runes) > 3 {
		return string(runes[:3]) + "..."
	}
	return area
}

// MaskPlace 脱敏详细地址：全模糊处理
func MaskPlace(place string) string {
	if place == "" {
		return ""
	}
	return "***"
}

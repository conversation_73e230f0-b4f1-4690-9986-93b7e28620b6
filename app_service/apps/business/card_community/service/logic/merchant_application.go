package logic

import (
	"context"

	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	"app_service/pkg/search"
)

// CheckUserMerchantStatus 检查用户是否为商家
// 通过查询商家申请记录，判断用户是否有审核通过的申请
func CheckUserMerchantStatus(ctx context.Context, userID string) bool {
	// 查询用户的商家申请记录
	merchantAppSchema := repo.GetQuery().MerchantApplication
	queryWrapper := search.NewQueryBuilder().
		Eq(merchantAppSchema.UserID, userID).
		Eq(merchantAppSchema.Status, int32(enums.ApplicationStatusApproved)).
		Build()

	application, err := repo.NewMerchantApplicationRepo(merchantAppSchema.WithContext(ctx)).SelectOne(queryWrapper)
	if err != nil {
		// 如果查询出错或没有记录，返回false
		return false
	}

	// 如果找到审核通过的申请记录，返回true
	return application != nil
}

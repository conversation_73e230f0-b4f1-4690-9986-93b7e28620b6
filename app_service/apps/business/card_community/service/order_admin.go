package service

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/repo"
	"app_service/apps/business/card_community/service/logic"
	commondefine "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/dal/model/mongdb"
	"app_service/apps/platform/user/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/excelize_lib"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/gin-gonic/gin"
)

// GetOrderListForAdmin 获取管理端订单列表
func (s *Service) GetOrderListForAdmin(req *define.GetOrderAdminListReq) (*define.GetOrderAdminListResp, error) {
	log.Ctx(s.ctx).Info("GetOrderListForAdmin called")

	// 构建查询条件
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(s.ctx))

	queryBuilder := search.NewQueryBuilder()

	// 订单状态筛选
	if req.Status != nil {
		queryBuilder = queryBuilder.Eq(cardOrderSchema.Status, req.Status.Int32())
	}

	// 时间范围筛选
	if !req.StartTime.IsZero() {
		queryBuilder = queryBuilder.Gte(cardOrderSchema.CreatedAt, req.StartTime)
	}
	if !req.EndTime.IsZero() {
		queryBuilder = queryBuilder.Lte(cardOrderSchema.CreatedAt, req.EndTime)
	}

	// 订单号筛选
	if req.OrderID != "" {
		queryBuilder = queryBuilder.Eq(cardOrderSchema.ID, req.OrderID)
	}

	// 会话组ID筛选
	if req.ConversationGroupID != "" {
		queryBuilder = queryBuilder.Eq(cardOrderSchema.ConversationGroupID, req.ConversationGroupID)
	}

	// 买家ID筛选
	if req.BuyerID != "" {
		queryBuilder = queryBuilder.Eq(cardOrderSchema.BuyerID, req.BuyerID)
	}

	// 卖家ID筛选
	if req.SellerID != "" {
		queryBuilder = queryBuilder.Eq(cardOrderSchema.SellerID, req.SellerID)
	}

	// 按创建时间倒序排列
	queryBuilder = queryBuilder.OrderByDesc(cardOrderSchema.CreatedAt)

	queryWrapper := queryBuilder.Build()

	// 分页查询
	orders, total, err := cardOrderRepo.SelectPage(queryWrapper, req.GetPage(), req.GetPageSize())
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询订单列表失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("查询订单列表失败")
	}

	if len(orders) == 0 {
		return &define.GetOrderAdminListResp{
			List:  []*define.GetOrderAdminListData{},
			Total: total,
		}, nil
	}

	// 收集所有用户ID
	userIDs := make([]string, 0, len(orders)*2)
	for _, order := range orders {
		userIDs = append(userIDs, order.BuyerID, order.SellerID)
	}

	// 批量获取用户信息
	nodeUserMap, err := facade.GetNodeUserMap(s.ctx, userIDs)
	if err != nil {
		log.Ctx(s.ctx).Errorf("批量获取用户信息失败: %v", err)
		nodeUserMap = make(map[string]*mongdb.User)
	}

	// 构建响应数据
	list := make([]*define.GetOrderAdminListData, 0, len(orders))
	for _, order := range orders {
		// 获取买家信息
		buyerUser := nodeUserMap[order.BuyerID]
		buyerInfo := define.UserInfo{
			ID:     order.BuyerID,
			Name:   buyerUser.PatbgDetail.Nickname,
			Avatar: buyerUser.PatbgDetail.Avatar,
		}

		// 获取卖家信息
		sellerUser := nodeUserMap[order.SellerID]
		sellerInfo := define.UserInfo{
			ID:     order.SellerID,
			Name:   sellerUser.PatbgDetail.Nickname,
			Avatar: sellerUser.PatbgDetail.Avatar,
		}

		// 获取收货地址并脱敏
		shippingAddress, _ := order.GetShippingAddress()
		if shippingAddress != nil {
			shippingAddress = logic.MaskAddress(shippingAddress)
		}

		item := &define.GetOrderAdminListData{
			OrderID:             order.ID,
			ConversationGroupID: order.ConversationGroupID,
			FirstCardImageURL:   order.FirstCardImageURL,
			CardGroupsCount:     int(order.CardGroupsCount),
			TotalQuantity:       int(order.TotalQuantity),
			TotalAmount:         order.TotalAmount,
			SellerUserInfo:      sellerInfo,
			BuyerUserInfo:       buyerInfo,
			Status:              order.GetStatus(),
			PaymentAt:           order.PaymentAt,
			CreatedAt:           order.CreatedAt,
			DeliveredAt:         order.DeliveredAt,
			ReceivedAt:          order.ReceivedAt,
			ShippingAddress:     shippingAddress,
		}
		list = append(list, item)
	}

	log.Ctx(s.ctx).Infof("管理端查询订单列表成功，返回 %d 条记录，总计 %d 条", len(list), total)

	return &define.GetOrderAdminListResp{
		List:  list,
		Total: total,
	}, nil
}

// GetOrderDetailForAdmin 获取管理端订单详情
func (s *Service) GetOrderDetailForAdmin(req *define.GetOrderAdminDetailReq) (*define.GetOrderAdminDetailResp, error) {
	log.Ctx(s.ctx).Infof("GetOrderDetailForAdmin called with OrderID: %s", req.OrderID)

	// 查询订单信息
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(s.ctx))

	queryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.ID, req.OrderID).
		Build()

	order, err := cardOrderRepo.SelectOne(queryWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询订单失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("订单不存在")
	}

	// 批量获取用户信息
	userIDs := []string{order.BuyerID, order.SellerID}
	nodeUserMap, err := facade.GetNodeUserMap(s.ctx, userIDs)
	if err != nil {
		log.Ctx(s.ctx).Errorf("批量获取用户信息失败: %v", err)
		nodeUserMap = make(map[string]*mongdb.User)
	}

	// 构造买家信息
	buyerUser := nodeUserMap[order.BuyerID]
	buyerInfo := define.UserInfo{
		ID:     order.BuyerID,
		Name:   buyerUser.PatbgDetail.Nickname,
		Avatar: buyerUser.PatbgDetail.Avatar,
	}

	// 构造卖家信息
	sellerUser := nodeUserMap[order.SellerID]
	sellerInfo := define.UserInfo{
		ID:     order.SellerID,
		Name:   sellerUser.PatbgDetail.Nickname,
		Avatar: sellerUser.PatbgDetail.Avatar,
	}

	// 获取卡片信息
	cardItems, err := order.GetCardItems()
	if err != nil {
		log.Ctx(s.ctx).Errorf("解析卡片信息失败: %v", err)
		cardItems = []define.CardItem{}
	}

	// 获取收货地址信息并脱敏
	shippingAddress, err := order.GetShippingAddress()
	if err != nil {
		log.Ctx(s.ctx).Errorf("解析收货地址失败: %v", err)
		shippingAddress = nil
	} else if shippingAddress != nil {
		shippingAddress = logic.MaskAddress(shippingAddress)
	}

	// 构建响应数据
	response := &define.GetOrderAdminDetailResp{
		// 基本订单信息
		OrderID:             order.ID,
		TotalAmount:         order.TotalAmount,
		CardGroupsCount:     int(order.CardGroupsCount),
		TotalQuantity:       int(order.TotalQuantity),
		ConversationGroupID: order.ConversationGroupID,
		FirstCardImageURL:   order.FirstCardImageURL,
		Status:              order.GetStatus(),
		PayAmount:           order.PayAmount,
		PaymentMethod:       order.GetPaymentMethod(),
		TransactionNo:       order.TransactionNo,

		// 用户信息
		SellerUserInfo: sellerInfo,
		BuyerUserInfo:  buyerInfo,

		// 时间信息
		CreatedAt:   order.CreatedAt,
		PaymentAt:   order.PaymentAt,
		DeliveredAt: order.DeliveredAt,
		ReceivedAt:  order.ReceivedAt,

		// 收货信息
		ShippingAddress: shippingAddress,

		// 卡片信息
		CardItems: cardItems,
	}

	log.Ctx(s.ctx).Infof("管理端查询订单详情成功: %s", req.OrderID)

	return response, nil
}

// GetOrderAddressForAdminOpen 获取管理端订单收货地址（不脱敏）
func (s *Service) GetOrderAddressForAdminOpen(req *define.GetOrderAdminAddressOpenReq) (*define.GetOrderAdminAddressOpenResp, error) {
	log.Ctx(s.ctx).Infof("GetOrderAddressForAdminOpen called with OrderID: %s", req.OrderID)

	// 查询订单信息
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(s.ctx))

	queryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.ID, req.OrderID).
		Build()

	order, err := cardOrderRepo.SelectOne(queryWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询订单失败: %v", err)
		return nil, commondefine.CommonErr.SetMsg("订单不存在")
	}

	// 获取收货地址信息（不脱敏）
	shippingAddress, err := order.GetShippingAddress()
	if err != nil {
		log.Ctx(s.ctx).Errorf("解析收货地址失败: %v", err)
		shippingAddress = nil
	}

	// 构建响应数据
	response := &define.GetOrderAdminAddressOpenResp{
		OrderID:         order.ID,
		ShippingAddress: shippingAddress,
	}

	log.Ctx(s.ctx).Infof("管理端查询订单收货地址（不脱敏）成功: %s", req.OrderID)

	return response, nil
}

// ExportOrdersForAdmin 导出管理端订单数据
func (s *Service) ExportOrdersForAdmin(ctx *gin.Context, req *define.ExportOrderAdminReq) error {
	log.Ctx(s.ctx).Info("ExportOrdersForAdmin called")

	// 检查时间范围（参考GetShipManageListExport的验证逻辑）
	//hasTimeRange := !req.StartTime.IsZero() && !req.EndTime.IsZero()
	//if hasTimeRange {
	//	// 结束时间必须在开始时间之后
	//	if req.EndTime.Before(req.StartTime) {
	//		return commondefine.ParamErr.SetMsg("结束时间必须在开始时间之后")
	//	}
	//	// 最多限制导出 31 天范围的数据
	//	if req.StartTime.AddDate(0, 0, 31).Before(req.EndTime) {
	//		return commondefine.ParamErr.SetMsg("最多只能导出31天范围的数据")
	//	}
	//}

	// 1. 初始化数据列表
	dataList := make([]*define.GetOrderAdminListData, 0)

	// 2. 分页获取所有数据
	for page := 1; page < 10000; page++ {
		// 构建分页请求
		listReq := &define.GetOrderAdminListReq{
			Status:              req.Status,
			StartTime:           req.StartTime,
			EndTime:             req.EndTime,
			OrderID:             req.OrderID,
			ConversationGroupID: req.ConversationGroupID,
			BuyerID:             req.BuyerID,
			BuyerPhone:          req.BuyerPhone,
			SellerID:            req.SellerID,
			SellerPhone:         req.SellerPhone,
		}
		listReq.Page = page
		listReq.PageSize = 100 // 每页100条

		// 调用现有的获取列表方法
		resp, err := s.GetOrderListForAdmin(listReq)
		if err != nil {
			return err
		}

		// 如果没有数据或数据为空，则结束循环
		if resp == nil || len(resp.List) == 0 {
			break
		}

		// 将当前页数据添加到总列表
		dataList = append(dataList, resp.List...)

		// 如果当前页数据小于分页大小，说明是最后一页
		if len(resp.List) < 100 {
			break
		}
	}

	// 3. 准备导出数据
	excel := excelize_lib.NewExcel()
	dataKey := make([]map[string]string, 0)
	// 定义导出列
	dataKey = append(dataKey, map[string]string{"key": "created_at", "title": "创建时间", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "order_id", "title": "订单号", "width": "24", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "conversation_group_id", "title": "会话ID", "width": "24", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "card_groups_count", "title": "交易数量", "width": "10", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "total_amount", "title": "订单金额", "width": "15", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "seller_name", "title": "卖家昵称", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "seller_id", "title": "卖家ID", "width": "24", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "buyer_name", "title": "买家昵称", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "buyer_id", "title": "买家ID", "width": "24", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "status", "title": "订单状态", "width": "15", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "payment_at", "title": "付款时间", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "delivered_at", "title": "发货时间", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "received_at", "title": "终态时间", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "receiver_name", "title": "收货人", "width": "15", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "receiver_phone", "title": "收货电话", "width": "15", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "receiver_address", "title": "收货地址", "width": "40", "is_num": "0"})

	// 4. 准备数据
	data := make([]map[string]interface{}, 0, len(dataList))
	for _, item := range dataList {
		// 格式化时间
		createdAt := util.GetDateTimeFormatStr(item.CreatedAt)
		paymentAt := ""
		if item.PaymentAt != nil {
			paymentAt = util.GetDateTimeFormatStr(*item.PaymentAt)
		}
		deliveredAt := ""
		if item.DeliveredAt != nil {
			deliveredAt = util.GetDateTimeFormatStr(*item.DeliveredAt)
		}
		receivedAt := ""
		if item.ReceivedAt != nil {
			receivedAt = util.GetDateTimeFormatStr(*item.ReceivedAt)
		}

		// 格式化金额（分转元）
		totalAmount := ""
		if item.TotalAmount > 0 {
			totalAmount = "¥" + util.FenToYuanString(int32(item.TotalAmount))
		}

		// 收货地址信息
		receiverName := ""
		receiverPhone := ""
		receiverAddress := ""
		if item.ShippingAddress != nil {
			receiverName = item.ShippingAddress.Name
			receiverPhone = item.ShippingAddress.MobilePhone
			receiverAddress = item.ShippingAddress.Area + item.ShippingAddress.Place
		}

		// 构建数据行
		row := map[string]interface{}{
			"created_at":            createdAt,                         // 创建时间
			"order_id":              item.OrderID,                      // 订单号
			"conversation_group_id": item.ConversationGroupID,          // 会话ID
			"card_groups_count":     util.StrVal(item.CardGroupsCount), // 交易数量
			"total_amount":          totalAmount,                       // 订单金额
			"seller_name":           item.SellerUserInfo.Name,          // 卖家昵称
			"seller_id":             item.SellerUserInfo.ID,            // 卖家ID
			"buyer_name":            item.BuyerUserInfo.Name,           // 买家昵称
			"buyer_id":              item.BuyerUserInfo.ID,             // 买家ID
			"status":                item.Status.String(),              // 订单状态
			"payment_at":            paymentAt,                         // 付款时间
			"delivered_at":          deliveredAt,                       // 发货时间
			"received_at":           receivedAt,                        // 终态时间
			"receiver_name":         receiverName,                      // 收货人
			"receiver_phone":        receiverPhone,                     // 收货电话
			"receiver_address":      receiverAddress,                   // 收货地址
		}

		data = append(data, row)
	}

	log.Ctx(s.ctx).Debugf("导出订单列表, 数据条数: %d", len(data))

	// 5. 导出Excel
	err := excel.ExportToStream(dataKey, data, ctx)
	if err != nil {
		log.Ctx(s.ctx).Errorf("failed to export order list: %v", err)
		return err
	}

	return nil
}

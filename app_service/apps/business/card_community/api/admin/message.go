package admin

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetMessageListForAdmin
// @Summary 获取消息列表（管理端）
// @Description 管理员查看指定会话的消息列表，支持分页
// @Tags 管理端-卡牌集社
// @x-apifox-folder "管理端/卡牌集市/消息管理"
// @Param data query define.GetMessageAdminListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetMessageAdminListResp}
// @Router /admin/v1/conversations/messages/list [get]
func GetMessageListForAdmin(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetMessageAdminListReq{}, s.<PERSON>MessageListForAdmin)
}

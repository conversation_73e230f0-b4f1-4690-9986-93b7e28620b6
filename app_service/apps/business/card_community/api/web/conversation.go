package web

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// CreateConversation
// @Summary 创建会话
// @Description 创建或获取与指定用户的会话，基于双向记录设计，会自动创建双方的会话记录
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/会话管理"
// @Param data body define.CreateConversationReq true "创建会话参数"
// @Success 200 {object} response.Data{data=define.CreateConversationResp}
// @Router /web/v1/conversations/add [post]
func CreateConversation(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.CreateConversationReq{}, s.CreateConversation)
}

// GetConversationList
// @Summary 获取会话列表
// @Description 获取当前用户的会话列表，基于双向记录设计，每个用户只能看到自己的会话记录。返回数据中包含对方的信息（other_participant_*字段），客户端直接使用这些字段显示对方信息即可
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/会话管理"
// @Param data query define.GetConversationListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetConversationListResp}
// @Router /web/v1/conversations/list [get]
func GetConversationList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetConversationListReq{}, s.GetConversationList)
}

// GetConversationDetail
// @Summary 获取会话详情
// @Description 获取会话详情信息
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/会话管理"
// @Param data query define.GetConversationDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetConversationDetailResp}
// @Router /web/v1/conversations/detail [get]
func GetConversationDetail(ctx *gin.Context) {
	s := service.New(ctx)
	// 从路径参数获取ID并构造请求结构体
	req := &define.GetConversationDetailReq{
		ID: ctx.Param("id"),
	}
	gin_request_process.MainHandle(ctx, req, s.GetConversationDetail)
}

package web

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// CreateOrder
// @Summary 创建订单
// @Description 在聊天中创建交易订单，包含卡片信息和价格
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/卡牌订单"
// @Param data body define.CreateOrderRequest true "创建订单参数"
// @Success 200 {object} response.Data{data=define.CreateOrderResponse}
// @Router /web/v1/card_community/orders/create [post]
func CreateOrder(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.CreateOrderRequest{}, s.CreateOrder)
}

// GetOrderDetail
// @Summary 获取订单详情
// @Description 获取指定订单的详细信息，包含买家卖家信息和订单状态
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/卡牌订单"
// @Param data query define.GetOrderDetailRequest true "查询参数"
// @Success 200 {object} response.Data{data=define.OrderDetailResponse}
// @Router /web/v1/card_community/orders/detail [get]
func GetOrderDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetOrderDetailRequest{}, s.GetOrderDetail)
}

// GetOrderList
// @Summary 订单列表（统一接口）
// @Description 根据用户类型获取买家或卖家的订单列表，支持状态筛选和分页
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/卡牌订单"
// @Param data query define.GetOrderListRequest true "查询参数"
// @Success 200 {object} response.Data{data=define.GetOrderListResponse}
// @Router /web/v1/card_community/orders/list [get]
func GetOrderList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetOrderListRequest{}, s.GetOrderList)
}

// GetOrderStats
// @Summary 订单状态统计
// @Description 获取订单状态统计，返回买家全部统计和卖家待发货统计
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/卡牌订单"
// @Param data query define.GetOrderStatsRequest true "查询参数"
// @Success 200 {object} response.Data{data=define.OrderStatsResponse}
// @Router /web/v1/card_community/orders/stats [get]
func GetOrderStats(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetOrderStatsRequest{}, s.GetOrderStats)
}

// PayOrder
// @Summary 支付订单
// @Description 处理订单支付流程，包括实名认证和收货地址校验
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/卡牌订单"
// @Param data body define.PayOrderRequest true "支付订单参数"
// @Success 200 {object} response.Data{data=define.PayOrderResponse}
// @Router /web/v1/card_community/orders/pay [post]
func PayOrder(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.PayOrderRequest{}, s.PayOrder)
}

// CancelOrder
// @Summary 取消订单
// @Description 根据取消类型取消订单，更新订单状态
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/卡牌订单"
// @Param data body define.CancelOrderRequest true "取消订单参数"
// @Success 200 {object} response.Data{data=define.OrderOperationResponse}
// @Router /web/v1/card_community/orders/cancel [post]
func CancelOrder(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.CancelOrderRequest{}, s.CancelOrder)
}

// DeleteOrder
// @Summary 删除订单
// @Description 软删除订单记录，仅对当前用户隐藏
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/卡牌订单"
// @Param data body define.DeleteOrderRequest true "删除订单参数"
// @Success 200 {object} response.Data{data=define.OrderOperationResponse}
// @Router /web/v1/card_community/orders/delete [post]
func DeleteOrder(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.DeleteOrderRequest{}, s.DeleteOrder)
}

// ShipOrder
// @Summary 卖家发货
// @Description 卖家确认发货，更新订单状态为待收货
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/卡牌订单"
// @Param data body define.ShipOrderRequest true "发货参数"
// @Success 200 {object} response.Data{data=define.OrderOperationResponse}
// @Router /web/v1/card_community/orders/ship [post]
func ShipOrder(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.ShipOrderRequest{}, s.ShipOrder)
}

// ConfirmReceived
// @Summary 买家确认收货
// @Description 买家确认收货，完成订单交易
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/卡牌订单"
// @Param data body define.ConfirmOrderRequest true "确认收货参数"
// @Success 200 {object} response.Data{data=define.OrderOperationResponse}
// @Router /web/v1/card_community/orders/confirm [post]
func ConfirmReceived(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.ConfirmOrderRequest{}, s.ConfirmReceived)
}

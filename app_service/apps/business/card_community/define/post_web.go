package define

import (
	"time"

	"app_service/apps/business/card_community/define/enums"
	"app_service/pkg/pagination"
)

// 帖子列表相关结构体
type (
	// GetPostListReq 获取帖子列表请求
	GetPostListReq struct {
		pagination.Pagination
		// Keyword   string  `form:"keyword" json:"keyword" binding:"max=50"` // 关键字(搜索描述，限输50个字符)
		// MinPrice  float64 `form:"min_price" json:"min_price"`              // 最低价格
		// MaxPrice  float64 `form:"max_price" json:"max_price"`              // 最高价格
		// OrderBy   string  `form:"order_by" json:"order_by"`                // 排序字段，支持：created_at, price
		// SortOrder string  `form:"sort_order" json:"sort_order"`            // 排序方式，desc: 倒序，asc: 升序
	}

	// GetPostListData 帖子列表数据
	GetPostListData struct {
		ID           string      `json:"id"`            // 帖子ID
		MerchantID   string      `json:"merchant_id"`   // 商家ID
		MerchantInfo UserInfo    `json:"merchant_info"` // 商家信息
		Description  string      `json:"description"`   // 帖子描述
		Price        int64       `json:"price"`         // 收购价格
		MediaFiles   []MediaFile `json:"media_files"`   // 媒体文件
		CreatedAt    time.Time   `json:"created_at"`    // 创建时间
	}

	// GetPostListResp 获取帖子列表响应
	GetPostListResp struct {
		List    []*GetPostListData `json:"list"`     // 帖子列表
		HasMore bool               `json:"has_more"` // 是否有更多
	}
)

// 帖子详情相关结构体
type (
	// GetPostDetailReq 获取帖子详情请求
	GetPostDetailReq struct {
		ID string `form:"id" json:"id" binding:"required"` // 帖子ID
	}

	// GetPostDetailResp 获取帖子详情响应
	GetPostDetailResp struct {
		ID           string           `json:"id"`            // 帖子ID
		MerchantID   string           `json:"merchant_id"`   // 商家ID
		MerchantInfo UserInfo         `json:"merchant_info"` // 商家信息
		Description  string           `json:"description"`   // 帖子描述
		Price        int64            `json:"price"`         // 收购价格
		MediaFiles   []MediaFile      `json:"media_files"`   // 媒体文件
		Status       enums.PostStatus `json:"status"`        // 帖子状态：-3=违规下架 -2=已下架 -1=已删除 1=已上架
		CreatedAt    time.Time        `json:"created_at"`    // 创建时间
		UpdatedAt    time.Time        `json:"updated_at"`    // 更新时间
	}
)

// 我的帖子列表相关结构体
type (
	// GetMyPostListReq 获取我的帖子列表请求
	GetMyPostListReq struct {
		pagination.Pagination
		Status enums.PostStatus `form:"status" json:"status"` // 帖子状态：-3=违规下架 -2=已下架 -1=已删除 1=已上架
	}

	// GetMyPostListData 我的帖子列表数据
	GetMyPostListData struct {
		ID          string           `json:"id"`          // 帖子ID
		Description string           `json:"description"` // 帖子描述
		Price       int64            `json:"price"`       // 收购价格
		MediaFiles  []MediaFile      `json:"media_files"` // 媒体文件
		Status      enums.PostStatus `json:"status"`      // 帖子状态：-3=违规下架 -2=已下架 -1=已删除 1=已上架
		CreatedAt   time.Time        `json:"created_at"`  // 创建时间
	}

	// GetMyPostListResp 获取我的帖子列表响应
	GetMyPostListResp struct {
		List    []*GetMyPostListData `json:"list"`     // 帖子列表
		HasMore bool                 `json:"has_more"` // 是否有更多
	}
)

// 创建帖子相关结构体
type (
	// CreatePostReq 创建帖子请求
	CreatePostReq struct {
		Description string      `json:"description" binding:"required,max=800"`      // 帖子描述
		Price       int64       `json:"price" binding:"required,min=0,max=999900"`   // 收购价格（分为单位，最多一位小数即10的倍数）
		MediaFiles  []MediaFile `json:"media_files" binding:"required,min=1,max=12"` // 媒体文件
	}

	// CreatePostResp 创建帖子响应
	CreatePostResp struct {
		ID string `json:"id"` // 帖子ID
	}
)

// 更新帖子状态相关结构体
type (
	// UpdatePostStatusReq 更新帖子状态请求
	UpdatePostStatusReq struct {
		ID     string           `json:"id" binding:"required"`     // 帖子ID
		Status enums.PostStatus `json:"status" binding:"required"` // 帖子状态：-3=违规下架 -2=已下架 -1=已删除 1=已上架
	}

	// UpdatePostStatusResp 更新帖子状态响应
	UpdatePostStatusResp struct {
		ID string `json:"id"` // 帖子ID
	}
)

// 编辑帖子相关结构体
type (
	// EditPostReq 编辑帖子请求
	EditPostReq struct {
		ID          string      `json:"id" binding:"required"`                       // 帖子ID
		Description string      `json:"description" binding:"required,max=800"`      // 帖子描述
		Price       int64       `json:"price" binding:"required,min=0,max=999900"`   // 收购价格（分为单位，最多一位小数即10的倍数）
		MediaFiles  []MediaFile `json:"media_files" binding:"required,min=1,max=12"` // 媒体文件
	}

	// EditPostResp 编辑帖子响应
	EditPostResp struct {
		ID string `json:"id"` // 帖子ID
	}
)

// 删除帖子相关结构体
type (
	// DeletePostReq 删除帖子请求
	DeletePostReq struct {
		ID string `json:"id" binding:"required"` // 帖子ID
	}

	// DeletePostResp 删除帖子响应
	DeletePostResp struct {
		ID string `json:"id"` // 帖子ID
	}
)

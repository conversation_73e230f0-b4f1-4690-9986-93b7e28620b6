package define

import (
	"app_service/apps/business/card_community/define/enums"
)

// MediaFile 媒体文件结构体
type MediaFile struct {
	URL          string          `json:"url" binding:"required"`    // 文件URL
	Type         enums.MediaType `json:"type" binding:"required"`   // 媒体类型：1=图片 2=视频
	Size         int64           `json:"size" binding:"required"`   // 文件大小(字节)
	Width        int             `json:"width" binding:"required"`  // 宽度(像素)
	Height       int             `json:"height" binding:"required"` // 高度(像素)
	Duration     int             `json:"duration"`                  // 视频时长(秒)，图片为0
	ThumbnailURL string          `json:"thumbnail_url"`             // 缩略图URL
}

// UserInfo 用户信息
type UserInfo struct {
	ID     string `json:"id"`     // 用户ID
	Name   string `json:"name"`   // 用户名称
	Avatar string `json:"avatar"` // 用户头像
}

// PostSnapshot 帖子快照信息
type PostSnapshot struct {
	Description string       `json:"description"` // 帖子文案描述
	Price       int64        `json:"price"`       // 收购价格
	MediaFiles  []*MediaFile `json:"media_files"` // 媒体文件数组
}

// CardItem 卡片项目
type CardItem struct {
	FrontImageURL string `json:"front_image_url" binding:"required"` // 正面图片URL
	BackImageURL  string `json:"back_image_url" binding:"required"`  // 反面图片URL
	Quantity      int    `json:"quantity" binding:"required,min=1"`  // 数量
}

// Address 收货地址结构
type Address struct {
	Name        string `json:"name" binding:"required"`         // 收货人姓名
	MobilePhone string `json:"mobile_phone" binding:"required"` // 收货人电话
	Code        string `json:"code" binding:"required"`         // 区域代码
	Area        string `json:"area" binding:"required"`         // 区域
	Place       string `json:"place" binding:"required"`        // 详细地址
}

// OrderSnapshot 订单快照信息
type OrderSnapshot struct {
	OrderID           string            `json:"order_id"`             // 订单ID
	Status            enums.OrderStatus `json:"status"`               // 订单状态
	TotalAmount       int64             `json:"total_amount"`         // 订单总金额（分）
	FirstCardImageURL string            `json:"first_card_image_url"` // 第一张卡牌图片URL
	CardGroupsCount   int               `json:"card_groups_count"`    // 卡片组数
	TotalQuantity     int               `json:"total_quantity"`       // 卡片总数量
}

// 推送通知相关结构体
type (
	// PushOrderRequest 订单推送请求
	PushOrderRequest struct {
		UserID      string              `json:"user_id"`      // 接收用户ID
		OrderID     string              `json:"order_id"`     // 订单ID
		PushType    enums.OrderPushType `json:"push_type"`    // 推送类型
		FromUser    string              `json:"from_user"`    // 来源用户昵称
		OrderStatus enums.OrderStatus   `json:"order_status"` // 订单状态
	}

	// PushMessageRequest 消息推送请求
	PushMessageRequest struct {
		UserID      string            `json:"user_id"`      // 接收用户ID
		MessageID   string            `json:"message_id"`   // 消息ID
		MessageType enums.MessageType `json:"message_type"` // 消息类型
		Content     string            `json:"content"`      // 消息内容
		FromUser    string            `json:"from_user"`    // 来源用户昵称
	}
)

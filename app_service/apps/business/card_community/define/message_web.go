package define

import (
	"time"

	"app_service/apps/business/card_community/define/enums"
	"app_service/pkg/pagination"
)

// 消息列表相关结构体
type (
	// GetMessageListReq 获取消息列表请求
	GetMessageListReq struct {
		pagination.Pagination
		ConversationID string `form:"conversation_id" json:"conversation_id" binding:"required"` // 会话ID
	}

	// GetMessageListData 消息列表数据
	GetMessageListData struct {
		ID            string            `json:"id"`                       // 消息ID
		ClientMsgID   string            `json:"client_msg_id"`            // 客户端生成的消息ID，用于幂等性控制
		MessageType   enums.MessageType `json:"message_type"`             // 消息类型：1=文本 2=图片 3=帖子快照 4=订单消息
		Content       string            `json:"content"`                  // 消息内容
		Media         *MediaFile        `json:"media,omitempty"`          // 媒体文件，可能为空
		PostID        string            `json:"post_id,omitempty"`        // 帖子ID（帖子消息类型时有值）
		PostSnapshot  *PostSnapshot     `json:"post_snapshot,omitempty"`  // 帖子快照信息（帖子消息类型时有值）
		OrderID       string            `json:"order_id,omitempty"`       // 订单ID（订单消息类型时有值）
		OrderSnapshot *OrderSnapshot    `json:"order_snapshot,omitempty"` // 订单快照信息（订单消息类型时有值）
		IsSmartReply  bool              `json:"is_smart_reply"`           // 是否为智能回复
		IsSent        bool              `json:"is_sent"`                  // 是否为当前用户发送的消息
		CreatedAt     time.Time         `json:"created_at"`               // 创建时间
	}

	// GetMessageListResp 获取消息列表响应
	GetMessageListResp struct {
		List    []*GetMessageListData `json:"list"`     // 消息列表
		HasMore bool                  `json:"has_more"` // 是否有更多
	}
)

// 发送消息相关结构体
type (
	// SendMessageReq 发送消息请求
	SendMessageReq struct {
		ClientMsgID     string            `json:"client_msg_id"`                                 // 客户端生成的消息ID，用于幂等性控制，（获取：请求/common/generate_id接口）
		ClientMsgNumber int64             `json:"client_msg_number"`                             // 客户端生成序号，用于消息排序，（获取：每个会话的消息从1开始单调递增（0是智能回复）），后端排序逻辑：序号倒序排，相同或没有，按创建时间倒序排
		ConversationID  string            `json:"conversation_id" binding:"required"`            // 会话ID
		ReceiverID      string            `json:"receiver_id" binding:"required"`                // 接收者ID
		MessageType     enums.MessageType `json:"message_type" binding:"required,oneof=1 2 3 4"` // 消息类型：1=文本 2=图片 3=帖子快照 4=订单消息
		Content         string            `json:"content"`                                       // 消息内容
		Media           *MediaFile        `json:"media,omitempty"`                               // 媒体文件，可能为空
		PostID          string            `json:"post_id,omitempty"`                             // 帖子ID（帖子消息类型时必填）
		PostSnapshot    *PostSnapshot     `json:"post_snapshot,omitempty"`                       // 帖子快照信息（帖子消息类型时必填）
		//OrderID         string            `json:"order_id,omitempty"`                            // 订单ID（订单消息类型时必填）
		//OrderSnapshot   *OrderSnapshot    `json:"order_snapshot,omitempty"`                      // 订单快照信息（订单消息类型时必填）
	}

	// SendMessageResp 发送消息响应
	SendMessageResp struct {
		ID        string    `json:"id"`         // 消息ID
		CreatedAt time.Time `json:"created_at"` // 创建时间
	}
)

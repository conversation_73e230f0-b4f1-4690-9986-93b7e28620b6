package define

import (
	"time"

	"app_service/apps/business/card_community/define/enums"
	"app_service/pkg/pagination"
)

// 管理端订单列表相关结构体
type (
	// GetOrderAdminListReq 获取管理端订单列表请求
	GetOrderAdminListReq struct {
		pagination.Pagination
		// 筛选条件
		Status              *enums.OrderStatus `form:"status" json:"status"`                               // 订单状态筛选: 0=未支付, 10=待发货, 20=待收货, 30=已完成, 40=已取消
		StartTime           time.Time          `form:"start_time" json:"start_time"`                       // 开始时间
		EndTime             time.Time          `form:"end_time" json:"end_time"`                           // 结束时间
		OrderID             string             `form:"order_id" json:"order_id"`                           // 订单号
		ConversationGroupID string             `form:"conversation_group_id" json:"conversation_group_id"` // 会话组ID
		BuyerID             string             `form:"buyer_id" json:"buyer_id"`                           // 买家ID
		BuyerPhone          string             `form:"buyer_phone" json:"buyer_phone"`                     // 买家手机号
		SellerID            string             `form:"seller_id" json:"seller_id"`                         // 卖家ID
		SellerPhone         string             `form:"seller_phone" json:"seller_phone"`                   // 卖家手机号
	}

	// GetOrderAdminListData 管理端订单列表数据
	GetOrderAdminListData struct {
		OrderID             string            `json:"order_id"`              // 订单ID
		ConversationGroupID string            `json:"conversation_group_id"` // 会话组ID
		FirstCardImageURL   string            `json:"first_card_image_url"`  // 第一张卡牌图片URL
		CardGroupsCount     int               `json:"card_groups_count"`     // 卡片组数
		TotalQuantity       int               `json:"total_quantity"`        // 卡片总数量
		TotalAmount         int64             `json:"total_amount"`          // 订单金额
		SellerUserInfo      UserInfo          `json:"seller_user_info"`      // 卖家用户信息
		BuyerUserInfo       UserInfo          `json:"buyer_user_info"`       // 买家用户信息
		Status              enums.OrderStatus `json:"status"`                // 订单状态: 0=未支付, 10=待发货, 20=待收货, 30=已完成, 40=已取消
		PaymentAt           *time.Time        `json:"payment_at"`            // 支付时间
		CreatedAt           time.Time         `json:"created_at"`            // 创建时间
		DeliveredAt         *time.Time        `json:"delivered_at"`          // 发货时间
		ReceivedAt          *time.Time        `json:"received_at"`           // 终态时间
		ShippingAddress     *Address          `json:"shipping_address"`      // 收货地址信息（脱敏）
	}

	// GetOrderAdminListResp 获取管理端订单列表响应
	GetOrderAdminListResp struct {
		List  []*GetOrderAdminListData `json:"list"`  // 订单列表
		Total int64                    `json:"total"` // 总数量
	}
)

// 管理端订单详情相关结构体
type (
	// GetOrderAdminDetailReq 获取管理端订单详情请求
	GetOrderAdminDetailReq struct {
		OrderID string `form:"order_id" json:"order_id" binding:"required"` // 订单ID
	}

	// GetOrderAdminDetailResp 获取管理端订单详情响应
	GetOrderAdminDetailResp struct {
		// 基本订单信息
		OrderID             string              `json:"order_id"`              // 订单编号
		TotalAmount         int64               `json:"total_amount"`          // 订单金额
		CardGroupsCount     int                 `json:"card_groups_count"`     // 交易数量（组数）
		TotalQuantity       int                 `json:"total_quantity"`        // 卡片总数量
		ConversationGroupID string              `json:"conversation_group_id"` // 会话组ID
		FirstCardImageURL   string              `json:"first_card_image_url"`  // 第一张卡牌图片URL
		Status              enums.OrderStatus   `json:"status"`                // 订单状态: 0=未支付, 10=待发货, 20=待收货, 30=已完成, 40=已取消
		PayAmount           int64               `json:"pay_amount"`            // 付款金额
		PaymentMethod       enums.PaymentMethod `json:"payment_method"`        // 支付渠道
		TransactionNo       string              `json:"transaction_no"`        // 流水单号

		// 用户信息
		SellerUserInfo UserInfo `json:"seller_user_info"` // 卖家用户信息
		BuyerUserInfo  UserInfo `json:"buyer_user_info"`  // 买家用户信息

		// 时间信息
		CreatedAt   time.Time  `json:"created_at"`   // 创建时间
		PaymentAt   *time.Time `json:"payment_at"`   // 支付时间
		DeliveredAt *time.Time `json:"delivered_at"` // 发货时间
		ReceivedAt  *time.Time `json:"received_at"`  // 终态时间（收货时间）

		// 收货信息
		ShippingAddress *Address `json:"shipping_address"` // 收货地址信息

		// 卡片信息
		CardItems []CardItem `json:"card_items"` // 卡片详情
	}
)

// 管理端订单导出相关结构体
type (
	// ExportOrderAdminReq 管理端订单导出请求
	ExportOrderAdminReq struct {
		// 筛选条件（与列表查询相同）
		Status              *enums.OrderStatus `form:"status" json:"status"`
		StartTime           time.Time          `form:"start_time" json:"start_time"`
		EndTime             time.Time          `form:"end_time" json:"end_time"`
		OrderID             string             `form:"order_id" json:"order_id"`
		ConversationGroupID string             `form:"conversation_group_id" json:"conversation_group_id"`
		BuyerID             string             `form:"buyer_id" json:"buyer_id"`
		BuyerPhone          string             `form:"buyer_phone" json:"buyer_phone"`
		SellerID            string             `form:"seller_id" json:"seller_id"`
		SellerPhone         string             `form:"seller_phone" json:"seller_phone"`
	}

	// ExportOrderAdminResp 管理端订单导出响应（导出接口直接返回文件流，无需响应体）
	ExportOrderAdminResp struct{}
)

// 管理端订单收货地址Open接口相关结构体（不脱敏）
type (
	// GetOrderAdminAddressOpenReq 获取管理端订单收货地址Open请求
	GetOrderAdminAddressOpenReq struct {
		OrderID string `form:"order_id" json:"order_id" binding:"required"` // 订单ID
	}

	// GetOrderAdminAddressOpenResp 获取管理端订单收货地址Open响应（不脱敏）
	GetOrderAdminAddressOpenResp struct {
		OrderID         string   `json:"order_id"`         // 订单编号
		ShippingAddress *Address `json:"shipping_address"` // 收货地址信息（不脱敏）
	}
)

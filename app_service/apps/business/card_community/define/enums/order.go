package enums

// OrderStatus 订单状态
type OrderStatus int

const (
	OrderStatusUnPaid      OrderStatus = 0  // 待支付
	OrderStatusUnDelivered OrderStatus = 10 // 待发货
	OrderStatusUnReceive   OrderStatus = 20 // 待收货
	OrderStatusCompleted   OrderStatus = 30 // 已完成
	OrderStatusCanceled    OrderStatus = 40 // 已取消
)

// String 返回订单状态的字符串表示
func (s OrderStatus) String() string {
	switch s {
	case OrderStatusUnPaid:
		return "待支付"
	case OrderStatusUnDelivered:
		return "待发货"
	case OrderStatusUnReceive:
		return "待收货"
	case OrderStatusCompleted:
		return "已完成"
	case OrderStatusCanceled:
		return "已取消"
	default:
		return "未知状态"
	}
}

// IsValid 检查订单状态是否有效
func (s OrderStatus) IsValid() bool {
	switch s {
	case OrderStatusUnPaid, OrderStatusUnDelivered, OrderStatusUnReceive, OrderStatusCompleted, OrderStatusCanceled:
		return true
	default:
		return false
	}
}

// Int32 返回订单状态的int32值
func (s OrderStatus) Int32() int32 {
	return int32(s)
}

// CanCancel 检查订单是否可以取消
func (s OrderStatus) CanCancel() bool {
	switch s {
	case OrderStatusUnPaid:
		return true
	default:
		return false
	}
}

// CanDelete 检查订单是否可以删除
func (s OrderStatus) CanDelete() bool {
	switch s {
	case OrderStatusCanceled, OrderStatusCompleted:
		return true
	default:
		return false
	}
}

// CancelType 取消类型
type CancelType int

const (
	CancelTypeBuyer   CancelType = 1 // 买家取消
	CancelTypeSeller  CancelType = 2 // 卖家取消
	CancelTypeTimeout CancelType = 3 // 超时取消
)

// String 返回取消类型的字符串表示
func (t CancelType) String() string {
	switch t {
	case CancelTypeBuyer:
		return "买家取消"
	case CancelTypeSeller:
		return "卖家取消"
	case CancelTypeTimeout:
		return "超时取消"
	default:
		return "未知类型"
	}
}

// IsValid 检查取消类型是否有效
func (t CancelType) IsValid() bool {
	switch t {
	case CancelTypeBuyer, CancelTypeSeller, CancelTypeTimeout:
		return true
	default:
		return false
	}
}

// Int32 返回取消类型的int32值
func (t CancelType) Int32() int32 {
	return int32(t)
}

// OrderPushType 订单推送类型
type OrderPushType int

const (
	PushTypeOrderUnPaid      OrderPushType = 1 // 待支付
	PushTypeOrderUnDelivered OrderPushType = 2 // 待发货
	PushTypeOrderUnReceive   OrderPushType = 3 // 待收货
	PushTypeOrderCompleted   OrderPushType = 4 // 已完成
)

// String 返回订单推送类型的字符串表示
func (t OrderPushType) String() string {
	switch t {
	case PushTypeOrderUnPaid:
		return "待支付"
	case PushTypeOrderUnDelivered:
		return "待发货"
	case PushTypeOrderUnReceive:
		return "待收货"
	case PushTypeOrderCompleted:
		return "已完成"
	default:
		return "未知类型"
	}
}

// IsValid 检查订单推送类型是否有效
func (t OrderPushType) IsValid() bool {
	switch t {
	case PushTypeOrderUnPaid, PushTypeOrderUnDelivered, PushTypeOrderUnReceive, PushTypeOrderCompleted:
		return true
	default:
		return false
	}
}

// Int32 返回订单推送类型的int32值
func (t OrderPushType) Int32() int32 {
	return int32(t)
}

// GetPushTitle 获取推送通知标题
func (t OrderPushType) GetPushTitle() string {
	switch t {
	case PushTypeOrderUnPaid:
		return "你有新的订单「待付款」"
	case PushTypeOrderUnDelivered:
		return "你有订单「待发货」"
	case PushTypeOrderUnReceive:
		return "你有订单「待收货」"
	case PushTypeOrderCompleted:
		return "交易完成"
	default:
		return "订单状态更新"
	}
}

// OrderMessageType 订单消息类型
type OrderMessageType int

const (
	OrderMessageTypeUnPaid      OrderMessageType = 1 // 待支付
	OrderMessageTypeUnDelivered OrderMessageType = 2 // 待发货
)

// String 返回订单消息类型的字符串表示
func (t OrderMessageType) String() string {
	switch t {
	case OrderMessageTypeUnPaid:
		return "待支付"
	case OrderMessageTypeUnDelivered:
		return "待发货"
	default:
		return "未知类型"
	}
}

// IsValid 检查订单消息类型是否有效
func (t OrderMessageType) IsValid() bool {
	switch t {
	case OrderMessageTypeUnPaid, OrderMessageTypeUnDelivered:
		return true
	default:
		return false
	}
}

// Int32 返回订单消息类型的int32值
func (t OrderMessageType) Int32() int32 {
	return int32(t)
}

// GetPushTitle 获取推送通知标题
func (t OrderMessageType) GetPushTitle() string {
	switch t {
	case OrderMessageTypeUnPaid:
		return "「交易订单 | 请确认，若无异议请尽快支付」"
	case OrderMessageTypeUnDelivered:
		return "「交易订单 | 已付款，请尽快发货」"
	default:
		return "订单状态更新"
	}
}

// UserType 用户类型（用于订单统计）
type UserType int

const (
	UserTypeBuyer  UserType = 1 // 买家
	UserTypeSeller UserType = 2 // 卖家
)

// String 返回用户类型的字符串表示
func (t UserType) String() string {
	switch t {
	case UserTypeBuyer:
		return "买家"
	case UserTypeSeller:
		return "卖家"
	default:
		return "未知类型"
	}
}

// IsValid 检查用户类型是否有效
func (t UserType) IsValid() bool {
	switch t {
	case UserTypeBuyer, UserTypeSeller:
		return true
	default:
		return false
	}
}

// Int32 返回用户类型的int32值
func (t UserType) Int32() int32 {
	return int32(t)
}

// PaymentMethod 支付方式
type PaymentMethod int

const (
	PaymentMethodCardWallet PaymentMethod = 1 // 卡牌钱包
)

// String 返回支付方式的字符串表示
func (m PaymentMethod) String() string {
	switch m {
	case PaymentMethodCardWallet:
		return "卡牌钱包"
	default:
		return "未知支付方式"
	}
}

// IsValid 检查支付方式是否有效
func (m PaymentMethod) IsValid() bool {
	switch m {
	case PaymentMethodCardWallet:
		return true
	default:
		return false
	}
}

// Int32 返回支付方式的int32值
func (m PaymentMethod) Int32() int32 {
	return int32(m)
}

// GetDisplayName 获取支付方式显示名称
func (m PaymentMethod) GetDisplayName() string {
	return m.String()
}

package enums

// MediaType 媒体类型
type MediaType int

const (
	MediaTypeImage MediaType = 1 // 图片
	MediaTypeVideo MediaType = 2 // 视频
)

// String 返回媒体类型的字符串表示
func (t MediaType) String() string {
	switch t {
	case MediaTypeImage:
		return "图片"
	case MediaTypeVideo:
		return "视频"
	default:
		return "未知类型"
	}
}

// IsValid 检查媒体类型是否有效
func (t MediaType) IsValid() bool {
	switch t {
	case MediaTypeImage, MediaTypeVideo:
		return true
	default:
		return false
	}
}

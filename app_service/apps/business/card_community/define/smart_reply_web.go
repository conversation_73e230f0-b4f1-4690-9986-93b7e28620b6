package define

import (
	"time"
)

// 智能回复模板相关结构体
type (
	// GetSmartReplyTemplateReq 获取智能回复模板请求
	GetSmartReplyTemplateReq struct {
		// 无需参数，从当前登录用户获取商家ID
	}

	// GetSmartReplyTemplateResp 获取智能回复模板响应
	GetSmartReplyTemplateResp struct {
		ID              string    `json:"id"`               // 模板ID
		MerchantID      string    `json:"merchant_id"`      // 商家ID
		TemplateContent string    `json:"template_content"` // 模板内容
		IsEnabled       bool      `json:"is_enabled"`       // 是否启用
		CreatedAt       time.Time `json:"created_at"`       // 创建时间
		UpdatedAt       time.Time `json:"updated_at"`       // 更新时间
	}
)

// 更新智能回复模板相关结构体
type (
	// UpdateSmartReplyTemplateReq 更新智能回复模板请求
	UpdateSmartReplyTemplateReq struct {
		TemplateContent string `json:"template_content" binding:"required,max=200"` // 模板内容，最多200字符
		Enabled         bool   `json:"enabled"`                                     // 是否启用
	}

	// UpdateSmartReplyTemplateResp 更新智能回复模板响应
	UpdateSmartReplyTemplateResp struct {
		ID        string    `json:"id"`         // 模板ID
		UpdatedAt time.Time `json:"updated_at"` // 更新时间
	}
)

// 切换智能回复开关相关结构体
type (
	// ToggleSmartReplyReq 切换智能回复开关请求
	ToggleSmartReplyReq struct {
		Enabled bool `json:"enabled"` // 是否启用
	}

	// ToggleSmartReplyResp 切换智能回复开关响应
	ToggleSmartReplyResp struct {
		Enabled bool `json:"enabled"` // 是否启用
	}
)

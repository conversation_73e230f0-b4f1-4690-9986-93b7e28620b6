package admin

import (
	"app_service/apps/business/card_community/api/admin"

	"github.com/gin-gonic/gin"
)

// Conversation 注册管理端会话相关路由
func Conversation(router *gin.RouterGroup) {
	// 会话管理相关路由
	conversationGroup := router.Group("/conversations")
	{
		// 获取会话列表
		conversationGroup.GET("/list", admin.GetConversationListForAdmin)
		// 获取会话详情
		conversationGroup.GET("/detail", admin.GetConversationDetailForAdmin)
	}
}

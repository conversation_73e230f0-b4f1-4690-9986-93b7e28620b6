package admin

import (
	"app_service/apps/business/card_community/api/admin"

	"github.com/gin-gonic/gin"
)

// Post 注册管理端帖子相关路由
func Post(router *gin.RouterGroup) {
	// 帖子管理相关路由
	postGroup := router.Group("/posts")
	{
		// 获取帖子列表
		postGroup.GET("/list", admin.GetPostListForAdmin)
		// 获取帖子详情
		postGroup.GET("/detail", admin.GetPostDetailForAdmin)
		// 更新帖子状态
		postGroup.POST("/edit", admin.UpdatePostStatusForAdmin)
	}
}

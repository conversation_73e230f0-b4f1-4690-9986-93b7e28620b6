package web

import (
	"app_service/apps/business/card_community/api/web"

	"github.com/gin-gonic/gin"
)

// SmartReply 注册智能回复相关路由
func SmartReply(router *gin.RouterGroup) {
	// 智能回复管理相关路由
	smartReplyGroup := router.Group("/smart_reply_template")
	{
		// 获取智能回复模板
		smartReplyGroup.GET("/detail", web.GetSmartReplyTemplate)
		// 更新智能回复模板
		smartReplyGroup.POST("/edit", web.UpdateSmartReplyTemplate)
		// 切换智能回复开关
		smartReplyGroup.POST("/toggle", web.ToggleSmartReply)
	}
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/card_community/dal/model"
)

func newSmartReplyTemplate(db *gorm.DB, opts ...gen.DOOption) smartReplyTemplate {
	_smartReplyTemplate := smartReplyTemplate{}

	_smartReplyTemplate.smartReplyTemplateDo.UseDB(db, opts...)
	_smartReplyTemplate.smartReplyTemplateDo.UseModel(&model.SmartReplyTemplate{})

	tableName := _smartReplyTemplate.smartReplyTemplateDo.TableName()
	_smartReplyTemplate.ALL = field.NewAsterisk(tableName)
	_smartReplyTemplate.ID = field.NewString(tableName, "id")
	_smartReplyTemplate.MerchantID = field.NewString(tableName, "merchant_id")
	_smartReplyTemplate.TemplateContent = field.NewString(tableName, "template_content")
	_smartReplyTemplate.IsEnabled = field.NewBool(tableName, "is_enabled")
	_smartReplyTemplate.CreatedAt = field.NewTime(tableName, "created_at")
	_smartReplyTemplate.UpdatedAt = field.NewTime(tableName, "updated_at")

	_smartReplyTemplate.fillFieldMap()

	return _smartReplyTemplate
}

// smartReplyTemplate 智能回复模板表
type smartReplyTemplate struct {
	smartReplyTemplateDo

	ALL             field.Asterisk
	ID              field.String // 模板ID
	MerchantID      field.String // 商家ID
	TemplateContent field.String // 模板内容
	IsEnabled       field.Bool   // 是否启用
	CreatedAt       field.Time   // 创建时间
	UpdatedAt       field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (s smartReplyTemplate) Table(newTableName string) *smartReplyTemplate {
	s.smartReplyTemplateDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s smartReplyTemplate) As(alias string) *smartReplyTemplate {
	s.smartReplyTemplateDo.DO = *(s.smartReplyTemplateDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *smartReplyTemplate) updateTableName(table string) *smartReplyTemplate {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewString(table, "id")
	s.MerchantID = field.NewString(table, "merchant_id")
	s.TemplateContent = field.NewString(table, "template_content")
	s.IsEnabled = field.NewBool(table, "is_enabled")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")

	s.fillFieldMap()

	return s
}

func (s *smartReplyTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *smartReplyTemplate) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 6)
	s.fieldMap["id"] = s.ID
	s.fieldMap["merchant_id"] = s.MerchantID
	s.fieldMap["template_content"] = s.TemplateContent
	s.fieldMap["is_enabled"] = s.IsEnabled
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
}

func (s smartReplyTemplate) clone(db *gorm.DB) smartReplyTemplate {
	s.smartReplyTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s smartReplyTemplate) replaceDB(db *gorm.DB) smartReplyTemplate {
	s.smartReplyTemplateDo.ReplaceDB(db)
	return s
}

type smartReplyTemplateDo struct{ gen.DO }

type ISmartReplyTemplateDo interface {
	gen.SubQuery
	Debug() ISmartReplyTemplateDo
	WithContext(ctx context.Context) ISmartReplyTemplateDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISmartReplyTemplateDo
	WriteDB() ISmartReplyTemplateDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISmartReplyTemplateDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISmartReplyTemplateDo
	Not(conds ...gen.Condition) ISmartReplyTemplateDo
	Or(conds ...gen.Condition) ISmartReplyTemplateDo
	Select(conds ...field.Expr) ISmartReplyTemplateDo
	Where(conds ...gen.Condition) ISmartReplyTemplateDo
	Order(conds ...field.Expr) ISmartReplyTemplateDo
	Distinct(cols ...field.Expr) ISmartReplyTemplateDo
	Omit(cols ...field.Expr) ISmartReplyTemplateDo
	Join(table schema.Tabler, on ...field.Expr) ISmartReplyTemplateDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISmartReplyTemplateDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISmartReplyTemplateDo
	Group(cols ...field.Expr) ISmartReplyTemplateDo
	Having(conds ...gen.Condition) ISmartReplyTemplateDo
	Limit(limit int) ISmartReplyTemplateDo
	Offset(offset int) ISmartReplyTemplateDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISmartReplyTemplateDo
	Unscoped() ISmartReplyTemplateDo
	Create(values ...*model.SmartReplyTemplate) error
	CreateInBatches(values []*model.SmartReplyTemplate, batchSize int) error
	Save(values ...*model.SmartReplyTemplate) error
	First() (*model.SmartReplyTemplate, error)
	Take() (*model.SmartReplyTemplate, error)
	Last() (*model.SmartReplyTemplate, error)
	Find() ([]*model.SmartReplyTemplate, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SmartReplyTemplate, err error)
	FindInBatches(result *[]*model.SmartReplyTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SmartReplyTemplate) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISmartReplyTemplateDo
	Assign(attrs ...field.AssignExpr) ISmartReplyTemplateDo
	Joins(fields ...field.RelationField) ISmartReplyTemplateDo
	Preload(fields ...field.RelationField) ISmartReplyTemplateDo
	FirstOrInit() (*model.SmartReplyTemplate, error)
	FirstOrCreate() (*model.SmartReplyTemplate, error)
	FindByPage(offset int, limit int) (result []*model.SmartReplyTemplate, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISmartReplyTemplateDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s smartReplyTemplateDo) Debug() ISmartReplyTemplateDo {
	return s.withDO(s.DO.Debug())
}

func (s smartReplyTemplateDo) WithContext(ctx context.Context) ISmartReplyTemplateDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s smartReplyTemplateDo) ReadDB() ISmartReplyTemplateDo {
	return s.Clauses(dbresolver.Read)
}

func (s smartReplyTemplateDo) WriteDB() ISmartReplyTemplateDo {
	return s.Clauses(dbresolver.Write)
}

func (s smartReplyTemplateDo) Session(config *gorm.Session) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Session(config))
}

func (s smartReplyTemplateDo) Clauses(conds ...clause.Expression) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s smartReplyTemplateDo) Returning(value interface{}, columns ...string) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s smartReplyTemplateDo) Not(conds ...gen.Condition) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s smartReplyTemplateDo) Or(conds ...gen.Condition) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s smartReplyTemplateDo) Select(conds ...field.Expr) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s smartReplyTemplateDo) Where(conds ...gen.Condition) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s smartReplyTemplateDo) Order(conds ...field.Expr) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s smartReplyTemplateDo) Distinct(cols ...field.Expr) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s smartReplyTemplateDo) Omit(cols ...field.Expr) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s smartReplyTemplateDo) Join(table schema.Tabler, on ...field.Expr) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s smartReplyTemplateDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISmartReplyTemplateDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s smartReplyTemplateDo) RightJoin(table schema.Tabler, on ...field.Expr) ISmartReplyTemplateDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s smartReplyTemplateDo) Group(cols ...field.Expr) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s smartReplyTemplateDo) Having(conds ...gen.Condition) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s smartReplyTemplateDo) Limit(limit int) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s smartReplyTemplateDo) Offset(offset int) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s smartReplyTemplateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s smartReplyTemplateDo) Unscoped() ISmartReplyTemplateDo {
	return s.withDO(s.DO.Unscoped())
}

func (s smartReplyTemplateDo) Create(values ...*model.SmartReplyTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s smartReplyTemplateDo) CreateInBatches(values []*model.SmartReplyTemplate, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s smartReplyTemplateDo) Save(values ...*model.SmartReplyTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s smartReplyTemplateDo) First() (*model.SmartReplyTemplate, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SmartReplyTemplate), nil
	}
}

func (s smartReplyTemplateDo) Take() (*model.SmartReplyTemplate, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SmartReplyTemplate), nil
	}
}

func (s smartReplyTemplateDo) Last() (*model.SmartReplyTemplate, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SmartReplyTemplate), nil
	}
}

func (s smartReplyTemplateDo) Find() ([]*model.SmartReplyTemplate, error) {
	result, err := s.DO.Find()
	return result.([]*model.SmartReplyTemplate), err
}

func (s smartReplyTemplateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SmartReplyTemplate, err error) {
	buf := make([]*model.SmartReplyTemplate, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s smartReplyTemplateDo) FindInBatches(result *[]*model.SmartReplyTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s smartReplyTemplateDo) Attrs(attrs ...field.AssignExpr) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s smartReplyTemplateDo) Assign(attrs ...field.AssignExpr) ISmartReplyTemplateDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s smartReplyTemplateDo) Joins(fields ...field.RelationField) ISmartReplyTemplateDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s smartReplyTemplateDo) Preload(fields ...field.RelationField) ISmartReplyTemplateDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s smartReplyTemplateDo) FirstOrInit() (*model.SmartReplyTemplate, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SmartReplyTemplate), nil
	}
}

func (s smartReplyTemplateDo) FirstOrCreate() (*model.SmartReplyTemplate, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SmartReplyTemplate), nil
	}
}

func (s smartReplyTemplateDo) FindByPage(offset int, limit int) (result []*model.SmartReplyTemplate, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s smartReplyTemplateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s smartReplyTemplateDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s smartReplyTemplateDo) Delete(models ...*model.SmartReplyTemplate) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *smartReplyTemplateDo) withDO(do gen.Dao) *smartReplyTemplateDo {
	s.DO = *do.(*gen.DO)
	return s
}

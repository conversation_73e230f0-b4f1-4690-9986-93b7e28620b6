// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                   = new(Query)
	CardOrder           *cardOrder
	Conversation        *conversation
	MerchantApplication *merchantApplication
	Message             *message
	Post                *post
	SmartReplyTemplate  *smartReplyTemplate
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	CardOrder = &Q.CardOrder
	Conversation = &Q.Conversation
	MerchantApplication = &Q.MerchantApplication
	Message = &Q.Message
	Post = &Q.Post
	SmartReplyTemplate = &Q.SmartReplyTemplate
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                  db,
		CardOrder:           newCardOrder(db, opts...),
		Conversation:        newConversation(db, opts...),
		MerchantApplication: newMerchantApplication(db, opts...),
		Message:             newMessage(db, opts...),
		Post:                newPost(db, opts...),
		SmartReplyTemplate:  newSmartReplyTemplate(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	CardOrder           cardOrder
	Conversation        conversation
	MerchantApplication merchantApplication
	Message             message
	Post                post
	SmartReplyTemplate  smartReplyTemplate
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                  db,
		CardOrder:           q.CardOrder.clone(db),
		Conversation:        q.Conversation.clone(db),
		MerchantApplication: q.MerchantApplication.clone(db),
		Message:             q.Message.clone(db),
		Post:                q.Post.clone(db),
		SmartReplyTemplate:  q.SmartReplyTemplate.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                  db,
		CardOrder:           q.CardOrder.replaceDB(db),
		Conversation:        q.Conversation.replaceDB(db),
		MerchantApplication: q.MerchantApplication.replaceDB(db),
		Message:             q.Message.replaceDB(db),
		Post:                q.Post.replaceDB(db),
		SmartReplyTemplate:  q.SmartReplyTemplate.replaceDB(db),
	}
}

type queryCtx struct {
	CardOrder           ICardOrderDo
	Conversation        IConversationDo
	MerchantApplication IMerchantApplicationDo
	Message             IMessageDo
	Post                IPostDo
	SmartReplyTemplate  ISmartReplyTemplateDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		CardOrder:           q.CardOrder.WithContext(ctx),
		Conversation:        q.Conversation.WithContext(ctx),
		MerchantApplication: q.MerchantApplication.WithContext(ctx),
		Message:             q.Message.WithContext(ctx),
		Post:                q.Post.WithContext(ctx),
		SmartReplyTemplate:  q.SmartReplyTemplate.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/card_community/dal/model"
)

func newMerchantApplication(db *gorm.DB, opts ...gen.DOOption) merchantApplication {
	_merchantApplication := merchantApplication{}

	_merchantApplication.merchantApplicationDo.UseDB(db, opts...)
	_merchantApplication.merchantApplicationDo.UseModel(&model.MerchantApplication{})

	tableName := _merchantApplication.merchantApplicationDo.TableName()
	_merchantApplication.ALL = field.NewAsterisk(tableName)
	_merchantApplication.ID = field.NewString(tableName, "id")
	_merchantApplication.UserID = field.NewString(tableName, "user_id")
	_merchantApplication.Status = field.NewInt32(tableName, "status")
	_merchantApplication.ReviewerID = field.NewString(tableName, "reviewer_id")
	_merchantApplication.AppliedAt = field.NewTime(tableName, "applied_at")
	_merchantApplication.ReviewedAt = field.NewTime(tableName, "reviewed_at")
	_merchantApplication.CreatedAt = field.NewTime(tableName, "created_at")
	_merchantApplication.UpdatedAt = field.NewTime(tableName, "updated_at")

	_merchantApplication.fillFieldMap()

	return _merchantApplication
}

// merchantApplication 商家申请表
type merchantApplication struct {
	merchantApplicationDo

	ALL        field.Asterisk
	ID         field.String // 申请ID
	UserID     field.String // 用户ID
	Status     field.Int32  // 状态：1=待审核 2=审核通过 -1=审核不通过
	ReviewerID field.String // 审核人ID
	AppliedAt  field.Time   // 申请时间
	ReviewedAt field.Time   // 审核时间
	CreatedAt  field.Time   // 创建时间
	UpdatedAt  field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (m merchantApplication) Table(newTableName string) *merchantApplication {
	m.merchantApplicationDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m merchantApplication) As(alias string) *merchantApplication {
	m.merchantApplicationDo.DO = *(m.merchantApplicationDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *merchantApplication) updateTableName(table string) *merchantApplication {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewString(table, "id")
	m.UserID = field.NewString(table, "user_id")
	m.Status = field.NewInt32(table, "status")
	m.ReviewerID = field.NewString(table, "reviewer_id")
	m.AppliedAt = field.NewTime(table, "applied_at")
	m.ReviewedAt = field.NewTime(table, "reviewed_at")
	m.CreatedAt = field.NewTime(table, "created_at")
	m.UpdatedAt = field.NewTime(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *merchantApplication) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *merchantApplication) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 8)
	m.fieldMap["id"] = m.ID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["status"] = m.Status
	m.fieldMap["reviewer_id"] = m.ReviewerID
	m.fieldMap["applied_at"] = m.AppliedAt
	m.fieldMap["reviewed_at"] = m.ReviewedAt
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m merchantApplication) clone(db *gorm.DB) merchantApplication {
	m.merchantApplicationDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m merchantApplication) replaceDB(db *gorm.DB) merchantApplication {
	m.merchantApplicationDo.ReplaceDB(db)
	return m
}

type merchantApplicationDo struct{ gen.DO }

type IMerchantApplicationDo interface {
	gen.SubQuery
	Debug() IMerchantApplicationDo
	WithContext(ctx context.Context) IMerchantApplicationDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMerchantApplicationDo
	WriteDB() IMerchantApplicationDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMerchantApplicationDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMerchantApplicationDo
	Not(conds ...gen.Condition) IMerchantApplicationDo
	Or(conds ...gen.Condition) IMerchantApplicationDo
	Select(conds ...field.Expr) IMerchantApplicationDo
	Where(conds ...gen.Condition) IMerchantApplicationDo
	Order(conds ...field.Expr) IMerchantApplicationDo
	Distinct(cols ...field.Expr) IMerchantApplicationDo
	Omit(cols ...field.Expr) IMerchantApplicationDo
	Join(table schema.Tabler, on ...field.Expr) IMerchantApplicationDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMerchantApplicationDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMerchantApplicationDo
	Group(cols ...field.Expr) IMerchantApplicationDo
	Having(conds ...gen.Condition) IMerchantApplicationDo
	Limit(limit int) IMerchantApplicationDo
	Offset(offset int) IMerchantApplicationDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMerchantApplicationDo
	Unscoped() IMerchantApplicationDo
	Create(values ...*model.MerchantApplication) error
	CreateInBatches(values []*model.MerchantApplication, batchSize int) error
	Save(values ...*model.MerchantApplication) error
	First() (*model.MerchantApplication, error)
	Take() (*model.MerchantApplication, error)
	Last() (*model.MerchantApplication, error)
	Find() ([]*model.MerchantApplication, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MerchantApplication, err error)
	FindInBatches(result *[]*model.MerchantApplication, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MerchantApplication) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMerchantApplicationDo
	Assign(attrs ...field.AssignExpr) IMerchantApplicationDo
	Joins(fields ...field.RelationField) IMerchantApplicationDo
	Preload(fields ...field.RelationField) IMerchantApplicationDo
	FirstOrInit() (*model.MerchantApplication, error)
	FirstOrCreate() (*model.MerchantApplication, error)
	FindByPage(offset int, limit int) (result []*model.MerchantApplication, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMerchantApplicationDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m merchantApplicationDo) Debug() IMerchantApplicationDo {
	return m.withDO(m.DO.Debug())
}

func (m merchantApplicationDo) WithContext(ctx context.Context) IMerchantApplicationDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m merchantApplicationDo) ReadDB() IMerchantApplicationDo {
	return m.Clauses(dbresolver.Read)
}

func (m merchantApplicationDo) WriteDB() IMerchantApplicationDo {
	return m.Clauses(dbresolver.Write)
}

func (m merchantApplicationDo) Session(config *gorm.Session) IMerchantApplicationDo {
	return m.withDO(m.DO.Session(config))
}

func (m merchantApplicationDo) Clauses(conds ...clause.Expression) IMerchantApplicationDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m merchantApplicationDo) Returning(value interface{}, columns ...string) IMerchantApplicationDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m merchantApplicationDo) Not(conds ...gen.Condition) IMerchantApplicationDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m merchantApplicationDo) Or(conds ...gen.Condition) IMerchantApplicationDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m merchantApplicationDo) Select(conds ...field.Expr) IMerchantApplicationDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m merchantApplicationDo) Where(conds ...gen.Condition) IMerchantApplicationDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m merchantApplicationDo) Order(conds ...field.Expr) IMerchantApplicationDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m merchantApplicationDo) Distinct(cols ...field.Expr) IMerchantApplicationDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m merchantApplicationDo) Omit(cols ...field.Expr) IMerchantApplicationDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m merchantApplicationDo) Join(table schema.Tabler, on ...field.Expr) IMerchantApplicationDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m merchantApplicationDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMerchantApplicationDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m merchantApplicationDo) RightJoin(table schema.Tabler, on ...field.Expr) IMerchantApplicationDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m merchantApplicationDo) Group(cols ...field.Expr) IMerchantApplicationDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m merchantApplicationDo) Having(conds ...gen.Condition) IMerchantApplicationDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m merchantApplicationDo) Limit(limit int) IMerchantApplicationDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m merchantApplicationDo) Offset(offset int) IMerchantApplicationDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m merchantApplicationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMerchantApplicationDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m merchantApplicationDo) Unscoped() IMerchantApplicationDo {
	return m.withDO(m.DO.Unscoped())
}

func (m merchantApplicationDo) Create(values ...*model.MerchantApplication) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m merchantApplicationDo) CreateInBatches(values []*model.MerchantApplication, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m merchantApplicationDo) Save(values ...*model.MerchantApplication) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m merchantApplicationDo) First() (*model.MerchantApplication, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MerchantApplication), nil
	}
}

func (m merchantApplicationDo) Take() (*model.MerchantApplication, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MerchantApplication), nil
	}
}

func (m merchantApplicationDo) Last() (*model.MerchantApplication, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MerchantApplication), nil
	}
}

func (m merchantApplicationDo) Find() ([]*model.MerchantApplication, error) {
	result, err := m.DO.Find()
	return result.([]*model.MerchantApplication), err
}

func (m merchantApplicationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MerchantApplication, err error) {
	buf := make([]*model.MerchantApplication, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m merchantApplicationDo) FindInBatches(result *[]*model.MerchantApplication, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m merchantApplicationDo) Attrs(attrs ...field.AssignExpr) IMerchantApplicationDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m merchantApplicationDo) Assign(attrs ...field.AssignExpr) IMerchantApplicationDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m merchantApplicationDo) Joins(fields ...field.RelationField) IMerchantApplicationDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m merchantApplicationDo) Preload(fields ...field.RelationField) IMerchantApplicationDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m merchantApplicationDo) FirstOrInit() (*model.MerchantApplication, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MerchantApplication), nil
	}
}

func (m merchantApplicationDo) FirstOrCreate() (*model.MerchantApplication, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MerchantApplication), nil
	}
}

func (m merchantApplicationDo) FindByPage(offset int, limit int) (result []*model.MerchantApplication, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m merchantApplicationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m merchantApplicationDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m merchantApplicationDo) Delete(models ...*model.MerchantApplication) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *merchantApplicationDo) withDO(do gen.Dao) *merchantApplicationDo {
	m.DO = *do.(*gen.DO)
	return m
}

package model

import (
	"app_service/apps/business/card_community/define/enums"
	"time"
)

// MarkReadForUser 标记用户已读
func (c *Conversation) MarkReadForUser() {
	now := time.Now()
	c.LastMessageTime = &now
}

// UpdateLastMessage 更新最后一条消息信息
func (c *Conversation) UpdateLastMessage(messageID string, content string) {
	now := time.Now()
	c.LastMessageID = messageID
	c.LastMessageContent = content
	c.LastMessageTime = &now
}

// SetStatus 设置状态
func (c *Conversation) SetStatus(limited bool) {
	if limited {
		c.Status = enums.StatusLimited.Int32() // 限制中
	} else {
		c.Status = enums.StatusNormal.Int32() // 正常
	}
}

// IsLimitedStatus 检查是否处于限制状态
func (c *Conversation) IsLimitedStatus() bool {
	return c.Status == enums.StatusLimited.Int32() // -1表示限制中
}

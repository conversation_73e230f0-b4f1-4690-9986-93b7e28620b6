// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameMerchantApplication = "merchant_applications"

// MerchantApplication 商家申请表
type MerchantApplication struct {
	ID         string     `gorm:"column:id;type:varchar(64);primaryKey;comment:申请ID" json:"id"`                                      // 申请ID
	UserID     string     `gorm:"column:user_id;type:varchar(64);not null;comment:用户ID" json:"user_id"`                              // 用户ID
	Status     int32      `gorm:"column:status;type:tinyint;not null;default:1;comment:状态：1=待审核 2=审核通过 -1=审核不通过" json:"status"`      // 状态：1=待审核 2=审核通过 -1=审核不通过
	ReviewerID string     `gorm:"column:reviewer_id;type:varchar(64);comment:审核人ID" json:"reviewer_id"`                              // 审核人ID
	AppliedAt  time.Time  `gorm:"column:applied_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:申请时间" json:"applied_at"` // 申请时间
	ReviewedAt *time.Time `gorm:"column:reviewed_at;type:datetime;comment:审核时间" json:"reviewed_at"`                                  // 审核时间
	CreatedAt  time.Time  `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt  time.Time  `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName MerchantApplication's table name
func (*MerchantApplication) TableName() string {
	return TableNameMerchantApplication
}

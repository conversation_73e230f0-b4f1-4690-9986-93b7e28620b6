// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
)

const TableNameMessage = "messages"

// Message 消息表
type Message struct {
	ID              string          `gorm:"column:id;type:varchar(64);primaryKey;comment:消息ID" json:"id"`                                                                                                                                                                         // 消息ID
	ClientMsgID     string          `gorm:"column:client_msg_id;type:varchar(64);not null;comment:客户端生成的消息ID，用于幂等性控制" json:"client_msg_id"`                                                                                                                                       // 客户端生成的消息ID，用于幂等性控制
	ClientMsgNumber int64           `gorm:"column:client_msg_number;type:bigint;comment:客户端生成序号，用于消息排序" json:"client_msg_number"`                                                                                                                                                 // 客户端生成序号，用于消息排序
	SmallUserID     string          `gorm:"column:small_user_id;type:varchar(64);not null;comment:较小的用户ID min(sender_id, receiver_id)" json:"small_user_id"`                                                                                                                      // 较小的用户ID min(sender_id, receiver_id)
	BigUserID       string          `gorm:"column:big_user_id;type:varchar(64);not null;comment:较大的用户ID max(sender_id, receiver_id)" json:"big_user_id"`                                                                                                                          // 较大的用户ID max(sender_id, receiver_id)
	Direction       int32           `gorm:"column:direction;type:tinyint;not null;comment:消息方向：-1=较大uid向较小uid发送消息，1=较小uid向较大uid发送消息" json:"direction"`                                                                                                                            // 消息方向：-1=较大uid向较小uid发送消息，1=较小uid向较大uid发送消息
	MessageType     int32           `gorm:"column:message_type;type:tinyint;not null;comment:消息类型：1=文本 2=图片 3=帖子快照 4=订单消息" json:"message_type"`                                                                                                                                   // 消息类型：1=文本 2=图片 3=帖子快照 4=订单消息
	Content         string          `gorm:"column:content;type:text;comment:消息内容" json:"content"`                                                                                                                                                                                 // 消息内容
	MediaFile       *datatypes.JSON `gorm:"column:media_file;type:json;comment:媒体文件信息，格式:{"url":"URL","type":1,"size":1024,"width":800,"height":600,"duration":30,"thumbnail_url":"缩略图URL"}" json:"media_file"`                                                                   // 媒体文件信息，格式:{"url":"URL","type":1,"size":1024,"width":800,"height":600,"duration":30,"thumbnail_url":"缩略图URL"}
	PostID          string          `gorm:"column:post_id;type:varchar(64);comment:关联的帖子ID（帖子消息类型时使用）" json:"post_id"`                                                                                                                                                            // 关联的帖子ID（帖子消息类型时使用）
	PostSnapshot    *datatypes.JSON `gorm:"column:post_snapshot;type:json;comment:帖子快照信息，格式:{"description":"文案","price":100.00,"media_files":[...]}" json:"post_snapshot"`                                                                                                        // 帖子快照信息，格式:{"description":"文案","price":100.00,"media_files":[...]}
	OrderID         string          `gorm:"column:order_id;type:varchar(64);comment:关联的订单ID（订单消息类型时使用）" json:"order_id"`                                                                                                                                                          // 关联的订单ID（订单消息类型时使用）
	OrderSnapshot   *datatypes.JSON `gorm:"column:order_snapshot;type:json;comment:订单快照信息，格式:{"order_id":"订单ID","status":"状态","total_amount":10000,"first_card_image":"URL","card_groups_count":1,"total_quantity":5,"created_at":"2024-01-01T00:00:00Z"}" json:"order_snapshot"` // 订单快照信息，格式:{"order_id":"订单ID","status":"状态","total_amount":10000,"first_card_image":"URL","card_groups_count":1,"total_quantity":5,"created_at":"2024-01-01T00:00:00Z"}
	IsSmartReply    bool            `gorm:"column:is_smart_reply;type:tinyint(1);not null;comment:是否智能回复：0=否 1=是" json:"is_smart_reply"`                                                                                                                                          // 是否智能回复：0=否 1=是
	AppChannel      string          `gorm:"column:app_channel;type:varchar(20);comment:应用渠道" json:"app_channel"`                                                                                                                                                                  // 应用渠道
	AppVersion      string          `gorm:"column:app_version;type:varchar(20);comment:应用版本号" json:"app_version"`                                                                                                                                                                 // 应用版本号
	ClientType      string          `gorm:"column:client_type;type:varchar(20);comment:客户端类型" json:"client_type"`                                                                                                                                                                 // 客户端类型
	IP              string          `gorm:"column:ip;type:varchar(255);comment:发送方IP地址" json:"ip"`                                                                                                                                                                                // 发送方IP地址
	CreatedAt       time.Time       `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                                                                                                                                    // 创建时间
	UpdatedAt       time.Time       `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                                                                                                                                    // 更新时间
}

// TableName Message's table name
func (*Message) TableName() string {
	return TableNameMessage
}

package model

import (
	"encoding/json"

	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"

	"gorm.io/datatypes"
)

// GetMediaFile 获取媒体文件信息
func (m *Message) GetMediaFile() (*define.MediaFile, error) {
	if m.MediaFile == nil {
		return nil, nil
	}

	var mediaFile define.MediaFile
	err := json.Unmarshal([]byte(*m.MediaFile), &mediaFile)
	if err != nil {
		return nil, err
	}
	return &mediaFile, nil
}

// SetMediaFile 设置媒体文件信息
func (m *Message) SetMediaFile(mediaFile *define.MediaFile) error {
	if mediaFile == nil {
		m.MediaFile = nil
		return nil
	}

	data, err := json.Marshal(mediaFile)
	if err != nil {
		return err
	}

	jsonData := datatypes.JSON(data)
	m.MediaFile = &jsonData
	return nil
}

// GetPostSnapshot 获取帖子快照信息
func (m *Message) GetPostSnapshot() (*define.PostSnapshot, error) {
	if m.PostSnapshot == nil {
		return nil, nil
	}

	var postSnapshot define.PostSnapshot
	err := json.Unmarshal([]byte(*m.PostSnapshot), &postSnapshot)
	if err != nil {
		return nil, err
	}
	return &postSnapshot, nil
}

// SetPostSnapshot 设置帖子快照信息
func (m *Message) SetPostSnapshot(postSnapshot *define.PostSnapshot) error {
	if postSnapshot == nil {
		m.PostSnapshot = nil
		return nil
	}

	data, err := json.Marshal(postSnapshot)
	if err != nil {
		return err
	}

	jsonData := datatypes.JSON(data)
	m.PostSnapshot = &jsonData
	return nil
}

// GetSenderID 根据方向获取发送者ID
func (m *Message) GetSenderID() string {
	if m.Direction == int32(enums.MessageDirectionSmallToBig) {
		return m.SmallUserID
	}
	return m.BigUserID
}

// GetReceiverID 根据方向获取接收者ID
func (m *Message) GetReceiverID() string {
	if m.Direction == int32(enums.MessageDirectionSmallToBig) {
		return m.BigUserID
	}
	return m.SmallUserID
}

// CalculateUserPair 计算用户对的大小用户ID和消息方向
func CalculateUserPair(senderID, receiverID string) (bigUserID, smallUserID string, direction int32) {
	// 字符串比较确定大小关系
	if senderID > receiverID {
		bigUserID = senderID
		smallUserID = receiverID
		direction = int32(enums.MessageDirectionBigToSmall) // -1
	} else {
		bigUserID = receiverID
		smallUserID = senderID
		direction = int32(enums.MessageDirectionSmallToBig) // 1
	}
	return
}

// IsSentByUser 判断消息是否由指定用户发送
func (m *Message) IsSentByUser(userID string) bool {
	return m.GetSenderID() == userID
}

// GetMessageType 获取消息类型枚举
func (m *Message) GetMessageType() enums.MessageType {
	return enums.MessageType(m.MessageType)
}

// GetDirection 获取消息方向枚举
func (m *Message) GetDirection() enums.MessageDirection {
	return enums.MessageDirection(m.Direction)
}

// GetOrderSnapshot 获取订单快照信息
func (m *Message) GetOrderSnapshot() (*define.OrderSnapshot, error) {
	if m.OrderSnapshot == nil {
		return nil, nil
	}

	var orderSnapshot define.OrderSnapshot
	err := json.Unmarshal([]byte(*m.OrderSnapshot), &orderSnapshot)
	if err != nil {
		return nil, err
	}
	return &orderSnapshot, nil
}

// SetOrderSnapshot 设置订单快照信息
func (m *Message) SetOrderSnapshot(orderSnapshot *define.OrderSnapshot) error {
	if orderSnapshot == nil {
		m.OrderSnapshot = nil
		return nil
	}

	data, err := json.Marshal(orderSnapshot)
	if err != nil {
		return err
	}

	jsonData := datatypes.JSON(data)
	m.OrderSnapshot = &jsonData
	return nil
}

// IsOrderMessage 判断是否为订单消息
func (m *Message) IsOrderMessage() bool {
	return m.GetMessageType() == enums.MessageTypeOrder
}

// HasOrderSnapshot 判断是否包含订单快照
func (m *Message) HasOrderSnapshot() bool {
	return m.OrderSnapshot != nil && m.OrderID != ""
}

// GetShardKey 获取分片键（用于分库分表路由）
func (m *Message) GetShardKey() string {
	return m.BigUserID + "_" + m.SmallUserID
}

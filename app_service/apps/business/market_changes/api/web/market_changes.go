package web

import (
	"app_service/apps/business/market_changes/define"
	"app_service/apps/business/market_changes/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetMarketChangesWebList
// @Summary 行情异动列表
// @Description 行情异动列表
// @Tags 用户端-行情异动
// @Param data query define.GetMarketChangesWebListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetMarketChangesWebListResp}
// @Router /web/v1/market_changes/list [get]
func GetMarketChangesWebList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetMarketChangesWebListReq{}, s.GetMarketChangesWebList)
}

// GetMarketChangesWebDetail
// @Summary 行情异动详情
// @Description 行情异动详情
// @Tags 用户端-行情异动
// @Param data query define.GetMarketChangesWebDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetMarketChangesWebDetailResp}
// @Router /web/v1/market_changes/detail [get]
func GetMarketChangesWebDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetMarketChangesWebDetailReq{}, s.GetMarketChangesWebDetail)
}

// GetLatestMarketChangesWebDetail
// @Summary 根据商品获取最新一条行情异动详情
// @Description 根据商品获取最新一条已发布的行情异动详情
// @Tags 用户端-行情异动
// @Param data query define.GetLatestMarketChangesWebDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetMarketChangesWebDetailResp} // 复用现有的响应结构体
// @Router /web/v1/market_changes/latest_detail [get]
func GetLatestMarketChangesWebDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetLatestMarketChangesWebDetailReq{}, s.GetLatestMarketChangesWebDetail)
}

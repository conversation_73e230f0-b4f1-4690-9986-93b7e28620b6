package open

import (
	"app_service/apps/business/market_changes/define"
	"app_service/apps/business/market_changes/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// SchedulePublish
// @Summary 定时发布行情异动
// @Description 定时发布行情异动
// @Tags Open-行情异动管理
// @Param data body define.SchedulePublishReq true "新增参数"
// @Success 200 {object} response.Data{data=define.SchedulePublishResp}
// @Router /open/v1/market_changes/schedule_publish [POST]
// @Security Bearer
func SchedulePublish(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.SchedulePublishReq{}, s.SchedulePublish)
}

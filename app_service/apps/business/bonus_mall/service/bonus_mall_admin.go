package service

import (
	"app_service/apps/business/bonus_mall/dal/model"
	"app_service/apps/business/bonus_mall/define"
	"app_service/apps/business/bonus_mall/define/enums"
	"app_service/apps/business/bonus_mall/repo"
	"app_service/apps/business/bonus_mall/service/locker"
	"app_service/apps/business/bonus_mall/service/logic"
	commonDefine "app_service/apps/platform/common/define"
	commonEnum "app_service/apps/platform/common/define/enum"
	commonLogic "app_service/apps/platform/common/service/logic"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/excelize_lib"
	"app_service/pkg/util/snowflakeutl"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
)

// AddBonusItem 添加积分商品
func (s *Service) AddBonusItem(req *define.AddBonusItemReq) (*define.AddBonusItemResp, error) {
	// 获取对应的实物商品
	steamItem, err := issueFacade.GetSteamItemByID(s.ctx, req.SteamItemID)
	if err != nil {
		return nil, err
	}
	// 获取该实物商品已经存在的积分商品
	bonusItemSchema := repo.GetQuery().BonusItem
	queryStatusList := []int32{enums.BonusItemStatusPending.Val(), enums.BonusItemStatusAvailable.Val()}
	qb := search.NewQueryBuilder().
		Eq(bonusItemSchema.SteamItemID, req.SteamItemID).
		In(bonusItemSchema.Status, queryStatusList).
		Build()
	existsBonusItems, err := repo.NewBonusItemRepo(bonusItemSchema.WithContext(s.ctx)).SelectList(qb)
	if err != nil {
		return nil, err
	}
	// 检查是否可以添加积分商品
	err = logic.CheckAddBonusItem(steamItem, req, existsBonusItems)
	if err != nil {
		return nil, err
	}

	adminID := s.GetAdminId()
	bonusItem := &model.BonusItem{
		BonusItemID:             snowflakeutl.GenerateID(),
		SteamItemID:             req.SteamItemID,
		SkuNo:                   steamItem.SkuNo,
		ItemName:                steamItem.ItemName,
		IconURL:                 steamItem.IconURL,
		ExchangePrice:           req.ExchangePrice,
		StockQty:                req.StockQty,
		ExchangeStartTime:       req.ExchangeStartTime,
		ExchangeEndTime:         req.ExchangeEndTime,
		PerUserLimitQty:         req.PerUserLimitQty,
		PerUserLimitRefreshRate: req.PerUserLimitRefreshRate,
		ExchangeUserType:        req.ExchangeUserType,
		Priority:                req.Priority,
		CreatedBy:               adminID,
	}
	err = repo.NewBonusItemRepo(bonusItemSchema.WithContext(s.ctx)).Save(bonusItem)
	if err != nil {
		return nil, err
	}

	// 保存操作日志
	go func() {
		ctx := context.Background()
		afterContent, _ := json.Marshal(bonusItem)
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:     bonusItem.BonusItemID,
			RelateType:   commonEnum.OperationLogRelateTypeBonusItem,
			Action:       commonEnum.OperationLogActionCreate,
			AfterContent: util.StrVal(afterContent),
			OperatedBy:   bonusItem.CreatedBy,
			OperatedAt:   util.Now(),
		})
	}()

	return &define.AddBonusItemResp{
		ID: bonusItem.BonusItemID,
	}, nil
}

// EditBonusItem 修改积分商品
func (s *Service) EditBonusItem(req *define.EditBonusItemReq) (*define.EditBonusItemResp, error) {
	bonusItemSchema := repo.GetQuery().BonusItem
	currentBonusItemQb := search.NewQueryBuilder().Eq(bonusItemSchema.BonusItemID, req.ID).Build()
	currentBonusItem, err := repo.NewBonusItemRepo(bonusItemSchema.WithContext(s.ctx)).SelectOne(currentBonusItemQb)
	if err != nil {
		return nil, err
	}

	// 上锁
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewBonusItemLock(util.StrVal(currentBonusItem.BonusItemID), locker.Exchange)))
	if !l.Lock(s.ctx) {
		return nil, define.BM300013Err.SetMsg("其他管理员在修改中，请稍后再试")
	}
	defer l.UnLock(s.ctx)

	// 获取对应的实物商品
	steamItemMap, err := issueFacade.GetSteamItemMap(s.ctx, []string{currentBonusItem.SteamItemID})
	if err != nil {
		return nil, err
	}
	steamItem, exists := steamItemMap[currentBonusItem.SteamItemID]
	if !exists {
		return nil, define.BM300003Err
	}
	// 获取该实物商品已经存在的积分商品
	queryStatusList := []int32{enums.BonusItemStatusPending.Val(), enums.BonusItemStatusAvailable.Val()}
	otherBonusItemQb := search.NewQueryBuilder().
		Ne(bonusItemSchema.BonusItemID, currentBonusItem.BonusItemID).
		Eq(bonusItemSchema.SteamItemID, currentBonusItem.SteamItemID).
		In(bonusItemSchema.Status, queryStatusList).
		Build()
	otherBonusItems, err := repo.NewBonusItemRepo(bonusItemSchema.WithContext(s.ctx)).SelectList(otherBonusItemQb)
	if err != nil {
		return nil, err
	}
	// 检查是否可以编辑积分商品
	err = logic.CheckEditBonusItem(steamItem, currentBonusItem, req, otherBonusItems)
	if err != nil {
		return nil, err
	}

	updateBonusItem := &model.BonusItem{
		BonusItemID:       req.ID,
		StockQty:          req.StockQty,
		ExchangeStartTime: req.ExchangeStartTime,
		ExchangeEndTime:   req.ExchangeEndTime,
		PerUserLimitQty:   req.PerUserLimitQty,
		ExchangeUserType:  req.ExchangeUserType,
		Priority:          req.Priority,
	}
	err = repo.NewBonusItemRepo(bonusItemSchema.WithContext(s.ctx)).UpdateById(updateBonusItem)
	if err != nil {
		return nil, err
	}

	// 保存操作日志
	adminID := s.GetAdminId()
	go func() {
		ctx := context.Background()
		beforeContent, _ := json.Marshal(currentBonusItem)
		afterContent, _ := json.Marshal(updateBonusItem)
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:      currentBonusItem.BonusItemID,
			RelateType:    commonEnum.OperationLogRelateTypeBonusItem,
			Action:        commonEnum.OperationLogActionUpdate,
			BeforeContent: util.StrVal(beforeContent),
			AfterContent:  util.StrVal(afterContent),
			OperatedBy:    adminID,
			OperatedAt:    util.Now(),
		})
	}()

	return &define.EditBonusItemResp{
		ID: updateBonusItem.BonusItemID,
	}, nil
}

// EditBonusItemStatus 修改积分商品状态
func (s *Service) EditBonusItemStatus(req *define.EditBonusItemStatusReq) (*define.EditBonusItemStatusResp, error) {
	bonusItemSchema := repo.GetQuery().BonusItem
	currentBonusItemQb := search.NewQueryBuilder().Eq(bonusItemSchema.BonusItemID, req.ID).Build()
	currentBonusItem, err := repo.NewBonusItemRepo(bonusItemSchema.WithContext(s.ctx)).SelectOne(currentBonusItemQb)
	if err != nil {
		return nil, err
	}

	err = logic.CheckEditBonusItemStatus(currentBonusItem, req)
	if err != nil {
		return nil, err
	}

	updateBonusItem := &model.BonusItem{
		BonusItemID: req.ID,
		Status:      req.Status,
	}
	err = repo.NewBonusItemRepo(bonusItemSchema.WithContext(s.ctx)).UpdateById(updateBonusItem)
	if err != nil {
		return nil, err
	}

	// 保存操作日志
	adminID := s.GetAdminId()
	go func() {
		ctx := context.Background()
		beforeContent := fmt.Sprintf(`{"status":%d}`, currentBonusItem.Status)
		afterContent := fmt.Sprintf(`{"status":%d}`, req.Status)
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:      currentBonusItem.BonusItemID,
			RelateType:    commonEnum.OperationLogRelateTypeBonusItem,
			Action:        commonEnum.OperationLogActionUpdate,
			BeforeContent: util.StrVal(beforeContent),
			AfterContent:  util.StrVal(afterContent),
			OperatedBy:    adminID,
			OperatedAt:    util.Now(),
		})
	}()

	return &define.EditBonusItemStatusResp{
		ID: updateBonusItem.BonusItemID,
	}, nil
}

// GetBonusItemAdminDetail 获取积分商品管理端详情
func (s *Service) GetBonusItemAdminDetail(req *define.GetBonusItemDetailAdminReq) (*define.GetBonusItemDetailAdminResp, error) {
	bonusItemSchema := repo.GetQuery().BonusItem
	currentBonusItemQb := search.NewQueryBuilder().Eq(bonusItemSchema.BonusItemID, req.ID).Build()
	currentBonusItem, err := repo.NewBonusItemRepo(bonusItemSchema.WithContext(s.ctx)).SelectOne(currentBonusItemQb)
	if err != nil {
		return nil, err
	}

	return &define.GetBonusItemDetailAdminResp{
		ID:                      currentBonusItem.BonusItemID,
		SteamItemID:             currentBonusItem.SteamItemID,
		ExchangePrice:           currentBonusItem.ExchangePrice,
		StockQty:                currentBonusItem.StockQty,
		ExchangeStartTime:       currentBonusItem.ExchangeStartTime,
		ExchangeEndTime:         currentBonusItem.ExchangeEndTime,
		PerUserLimitQty:         currentBonusItem.PerUserLimitQty,
		PerUserLimitRefreshRate: currentBonusItem.PerUserLimitRefreshRate,
		ExchangeUserType:        currentBonusItem.ExchangeUserType,
		Priority:                currentBonusItem.Priority,
		Status:                  currentBonusItem.Status,
	}, nil
}

// GetBonusItemAdminList 获取积分商品列表
func (s *Service) GetBonusItemAdminList(req *define.GetBonusItemListAdminReq) (*define.GetBonusItemListAdminResp, error) {
	bonusItemSchema := repo.GetQuery().BonusItem
	qb := search.NewQueryBuilder()
	if !req.StartTime.IsZero() && !req.EndTime.IsZero() {
		qb.Gte(bonusItemSchema.ExchangeStartTime, req.StartTime).Lte(bonusItemSchema.ExchangeStartTime, req.EndTime)
	}
	if req.ID != 0 {
		qb.Eq(bonusItemSchema.BonusItemID, req.ID)
	}
	if req.ItemKeyword != "" {
		if util.IsValidObjectID(req.ItemKeyword) {
			qb.Eq(bonusItemSchema.SteamItemID, req.ItemKeyword)
		} else {
			qb.Like(bonusItemSchema.ItemName, "%"+req.ItemKeyword+"%")
		}
	}
	if req.SkuNo != "" {
		qb.Eq(bonusItemSchema.SkuNo, req.SkuNo)
	}
	if req.Status != nil {
		qb.Eq(bonusItemSchema.Status, *req.Status)
	}
	// 排序
	qb.OrderByDesc(bonusItemSchema.CreatedAt)
	bonusItemList, total, err := repo.NewBonusItemRepo(bonusItemSchema.WithContext(s.ctx)).
		SelectPage(qb.Build(), req.GetPage(), req.GetPageSize())
	if err != nil {
		return nil, err
	}

	// 组装数据
	itemInfoList := make([]*define.BonusItemListAdminInfo, 0)
	for _, bonusItem := range bonusItemList {
		info := &define.BonusItemListAdminInfo{
			ID:                bonusItem.BonusItemID,
			SteamItemID:       bonusItem.SteamItemID,
			StockQty:          bonusItem.StockQty,
			ExchangePrice:     bonusItem.ExchangePrice,
			ExchangeStartTime: bonusItem.ExchangeStartTime,
			ExchangeEndTime:   bonusItem.ExchangeEndTime,
			PerUserLimitQty:   bonusItem.PerUserLimitQty,
			Priority:          bonusItem.Priority,
			Status:            bonusItem.Status,
			SkuNo:             bonusItem.SkuNo,
			ItemName:          bonusItem.ItemName,
			IconURL:           bonusItem.IconURL,
			ExchangedQty:      bonusItem.ExchangedQty,
		}
		itemInfoList = append(itemInfoList, info)
	}
	resp := &define.GetBonusItemListAdminResp{
		List:  itemInfoList,
		Total: total,
	}
	return resp, nil
}

// GetExchangeLogAdminList 获取积分商品兑换记录列表
func (s *Service) GetExchangeLogAdminList(req *define.GetExchangeLogListAdminReq) (*define.GetExchangeLogListAdminResp, error) {
	exLogSchema := repo.GetQuery().BonusItemExchangeLog
	qb := search.NewQueryBuilder()
	if !req.StartTime.IsZero() && !req.EndTime.IsZero() {
		qb.Gte(exLogSchema.ExchangeTime, req.StartTime).Lte(exLogSchema.ExchangeTime, req.EndTime)
	}
	if req.BonusItemID != 0 {
		qb.Eq(exLogSchema.BonusItemID, req.BonusItemID)
	}
	if req.ItemKeyword != "" {
		if util.IsValidObjectID(req.ItemKeyword) {
			qb.Eq(exLogSchema.SteamItemID, req.ItemKeyword)
		} else {
			qb.Like(exLogSchema.ItemName, "%"+req.ItemKeyword+"%")
		}
	}
	if req.SkuNo != "" {
		qb.Eq(exLogSchema.SkuNo, req.SkuNo)
	}
	if req.UserID != "" {
		qb.Eq(exLogSchema.UserID, req.UserID)
	}
	if req.MobilePhone != "" {
		qb.Eq(exLogSchema.MobilePhone, req.MobilePhone)
	}
	// 排序
	qb.OrderByDesc(exLogSchema.ExchangeTime)
	exchangeLogList, total, err := repo.NewBonusItemExchangeLogRepo(exLogSchema.WithContext(s.ctx)).
		SelectPage(qb.Build(), req.GetPage(), req.GetPageSize())
	if err != nil {
		return nil, err
	}

	// 组装数据
	exLogInfoList := make([]*define.ExchangeLogAdminInfo, 0)
	for _, exLog := range exchangeLogList {
		info := &define.ExchangeLogAdminInfo{
			ID:                exLog.BonusItemExchangeLogID,
			BonusItemID:       exLog.BonusItemID,
			SteamItemID:       exLog.SteamItemID,
			ExchangeQty:       exLog.ExchangeQty,
			ExchangePrice:     exLog.ExchangePrice,
			ExchangeTime:      exLog.ExchangeTime,
			BonusTotal:        exLog.BonusTotal,
			CostTotal:         exLog.CostTotal,
			SkuNo:             exLog.SkuNo,
			ItemName:          exLog.ItemName,
			IconURL:           exLog.IconURL,
			UserID:            exLog.UserID,
			MobilePhone:       util.PhoneMix(exLog.MobilePhone),
			NickName:          exLog.Nickname,
			Status:            exLog.Status,
			YcWithdrawOrderID: exLog.YcWithdrawOrderID,
		}
		exLogInfoList = append(exLogInfoList, info)
	}

	resp := &define.GetExchangeLogListAdminResp{
		List:  exLogInfoList,
		Total: total,
	}
	return resp, nil
}

// ExportExchangeLogList 导出兑换记录
func (s *Service) ExportExchangeLogList(ctx *gin.Context, req *define.GetExchangeLogListAdminReq) error {
	dataList := make([]*define.ExchangeLogAdminInfo, 0)
	for i := 1; i < 10000; i++ {
		req.PageSize = 100
		req.Page = i
		page, err := s.GetExchangeLogAdminList(req)
		if err != nil {
			return err
		}
		if page.List == nil || len(page.List) == 0 {
			break
		}
		dataList = append(dataList, page.List...)
	}
	excel := excelize_lib.NewExcel()
	dataKey := make([]map[string]string, 0)
	dataKey = append(dataKey, map[string]string{"key": "id", "title": "兑换单号", "width": "26", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "exchange_time", "title": "兑换时间", "width": "26", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "bonus_item_id", "title": "活动 ID", "width": "26", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "steam_item_id", "title": "商品 ID", "width": "26", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "sku_no", "title": "SKUID", "width": "26", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "item_name", "title": "商品名称", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "icon_url", "title": "商品图片", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "user_id", "title": "用户 ID", "width": "25", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "nick_name", "title": "用户昵称", "width": "18", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "mobile_phone", "title": "手机号", "width": "18", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "exchange_price", "title": "兑换单价", "width": "18", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "exchange_qty", "title": "兑换数量", "width": "10", "is_num": "1"})
	dataKey = append(dataKey, map[string]string{"key": "bonus_total", "title": "消耗积分", "width": "18", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "cost_total", "title": "合计成本", "width": "18", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "yc_withdraw_order_id", "title": "发货单号", "width": "30", "is_num": "0"})

	data := make([]map[string]interface{}, 0)
	for _, idata := range dataList {
		data = append(data, map[string]interface{}{
			"id":                   util.StrVal(idata.ID),
			"exchange_time":        util.GetDateTimeFormatStr(idata.ExchangeTime),
			"bonus_item_id":        util.StrVal(idata.BonusItemID),
			"steam_item_id":        idata.SteamItemID,
			"sku_no":               idata.SkuNo,
			"item_name":            idata.ItemName,
			"icon_url":             idata.IconURL,
			"user_id":              idata.UserID,
			"exchange_price":       util.StrVal(idata.ExchangePrice) + " 积分",
			"exchange_qty":         idata.ExchangeQty,
			"bonus_total":          util.StrVal(idata.BonusTotal) + " 积分",
			"cost_total":           "¥" + util.FenToYuanString(idata.CostTotal),
			"nick_name":            idata.NickName,
			"mobile_phone":         util.PhoneMix(idata.MobilePhone),
			"yc_withdraw_order_id": idata.YcWithdrawOrderID,
		})
	}

	err := excel.ExportToStream(dataKey, data, ctx)
	if err != nil {
		log.Ctx(s.ctx).Errorf("[Service.ExportExchangeLogList] ExportToWeb err:%v", err)
		return response.SystemErr
	}
	return nil
}

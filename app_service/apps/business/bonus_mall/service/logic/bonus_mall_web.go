package logic

import (
	"app_service/apps/business/bonus_mall/dal/model"
	"app_service/apps/business/bonus_mall/define"
	"app_service/apps/business/bonus_mall/define/enums"
	"app_service/apps/business/bonus_mall/repo"
	assetmodel "app_service/apps/platform/asset/dal/model"
	assetenum "app_service/apps/platform/asset/define/enums"
	asset_facade "app_service/apps/platform/asset/facade"
	assetrepo "app_service/apps/platform/asset/repo"
	commondefine "app_service/apps/platform/common/define"
	commonFacade "app_service/apps/platform/common/facade"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/third_party/yc_open"
	ycopendefine "app_service/third_party/yc_open/define"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"sync"
	"time"
)

// GetExchangeBonusItemDeps 并发获取兑换积分商品依赖数据
func GetExchangeBonusItemDeps(parentCtx context.Context, req *define.UserExchangeBonusItemReq,
	bonusItem *model.BonusItem, userID string) (*define.UserExchangeBonusItemDeps, error) {
	ctx, cancel := context.WithTimeout(parentCtx, time.Second*10)
	defer cancel()

	result := &define.UserExchangeBonusItemDeps{}
	errCh := make(chan error, 1)
	var wg sync.WaitGroup

	// 获取实物商品
	wg.Add(1)
	go func() {
		defer wg.Done()
		select {
		case <-ctx.Done():
			return
		default:
			steamItem, err := issueFacade.GetSteamItemByID(ctx, bonusItem.SteamItemID)
			if err != nil {
				select {
				case errCh <- err:
					cancel()
				default:
				}
			}
			result.SteamItem = steamItem
		}
	}()

	// 获取收货地址
	wg.Add(1)
	go func() {
		defer wg.Done()
		select {
		case <-ctx.Done():
			return
		default:
			address, err := issueFacade.GetAddressByID(ctx, req.AddressID)
			if err != nil {
				select {
				case errCh <- err:
					cancel()
				default:
				}
			}
			result.Address = address
		}
	}()

	// 获取用户钱包数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		select {
		case <-ctx.Done():
			return
		default:
			userWallet, err := asset_facade.GetUserWallet(ctx, userID)
			if err != nil {
				select {
				case errCh <- err:
					cancel()
				default:
				}
			}
			result.UserWallet = userWallet
		}
	}()

	// 等待所有协程执行完成
	go func() {
		wg.Wait()
		close(errCh)
	}()

	select {
	case err := <-errCh:
		return result, err
	case <-ctx.Done():
		return result, errors.New("[GetExchangeBonusItemDeps] execute concurrent tasks timeout")
	}
}

// CheckUserExchangeBonusItem 检查用户兑换积分商品
func CheckUserExchangeBonusItem(req *define.UserExchangeBonusItemReq, deps *define.UserExchangeBonusItemDeps) error {
	bonusItem := deps.BonusItem
	// 检查积分商品状态
	if bonusItem.Status != enums.BonusItemStatusAvailable.Val() {
		return define.BM300010Err
	}

	// 检查兑换活动是否开始
	if bonusItem.ExchangeStartTime.After(util.Now()) {
		return define.BM300009Err
	}

	// 检查库存（积分商品库存和实物商品库存）
	steamItem := deps.SteamItem
	if req.ExchangeQty > bonusItem.StockQty || req.ExchangeQty > steamItem.MarketPrices.Unx.SellListings {
		return define.BM300001Err
	}

	// 检查用户积分是否充足
	userWallet := deps.UserWallet
	if userWallet.Bonus < bonusItem.ExchangePrice {
		toastMsg := fmt.Sprintf("积分不足，您当前积分余额：%d", userWallet.Bonus)
		return define.BM300008Err.SetMsg(toastMsg)
	}

	// 检查是否超限
	if bonusItem.PerUserLimitQty > 0 {
		err := checkUserLimit(bonusItem, userWallet.UserID, req.ExchangeQty)
		if err != nil {
			if err.Error() == "LIMIT_EXCEED" {
				toastMsg := "你已超限，"
				if bonusItem.PerUserLimitRefreshRate == enums.PerUserLimitRefreshRateNotRefresh.Val() {
					toastMsg += fmt.Sprintf("限兑 %d 件", bonusItem.PerUserLimitQty)
				} else {
					toastMsg += fmt.Sprintf("每天限兑 %d 件", bonusItem.PerUserLimitQty)
				}
				return define.BM300007Err.SetMsg(toastMsg)
			}

			return err
		}
	}

	return nil
}

func GetPerUserLimitCacheKey(bonusItem *model.BonusItem, userID string) (string, int32) {
	cacheKey := fmt.Sprintf("app_service:bonus_item:%d:user_total:%s", bonusItem.BonusItemID, userID) // 不刷新
	var keyExpire int32
	if bonusItem.PerUserLimitRefreshRate == enums.PerUserLimitRefreshRateEveryDay.Val() {
		// 每天刷新
		date := util.Now().Format("2006-01-02")
		cacheKey = fmt.Sprintf("app_service:bonus_item:%d:user_daily:%s:%s", bonusItem.BonusItemID, userID, date)
		keyExpire = 60 * 60 * 24
	}

	return cacheKey, keyExpire
}

func checkUserLimit(bonusItem *model.BonusItem, userID string, exchangeQty int32) error {
	cacheKey, keyExpire := GetPerUserLimitCacheKey(bonusItem, userID)
	script := `
-- KEYS[1]: limit_key 限制 key
-- ARGV[1]: quantity 本次兑换数量
-- ARGV[2]: limit_quantity 每人限兑数量
-- ARGV[3]: key_expire key 过期时间

-- 获取当前计数
local cur_count = tonumber(redis.call('GET', KEYS[1])) or 0
local quantity = tonumber(ARGV[1])

-- 预检查限制（快速失败）
if quantity <= 0 then
    return {err = 'INVALID_QUANTITY'}
end

-- 每人限兑
if (cur_count + quantity) > tonumber(ARGV[2]) then
    return {err = 'LIMIT_EXCEED'}
end

-- 执行计数增加
redis.call('INCRBY', KEYS[1], quantity)

-- 设置每日Key过期时间（仅首次设置时）
local key_expire = tonumber(ARGV[3])
if cur_count == 0 and key_expire > 0 then
    redis.call('EXPIRE', KEYS[1], key_expire)
end

return {ok = 'SUCCESS'}
`
	keys := []string{cacheKey}
	values := []string{
		util.StrVal(exchangeQty),
		util.StrVal(bonusItem.PerUserLimitQty),
		util.StrVal(keyExpire),
	}
	_, err := global.REDIS.Eval(context.Background(), script, keys, values).Result()
	if err != nil {
		return err
	}

	return nil
}

// RollbackUserLimit 兑换失败时，回退用户限制
func RollbackUserLimit(ctx context.Context, bonusItem *model.BonusItem, userID string, exchangeQty int32) error {
	cacheKey, _ := GetPerUserLimitCacheKey(bonusItem, userID)
	script := `
local cur_count = tonumber(redis.call('GET', KEYS[1])) or 0
local quantity = tonumber(ARGV[1])

-- 已兑数量小于要回退的数量，则不进行回退操作
if cur_count < quantity then
return {ok = 'SUCCESS'}
end

redis.call('DECRBY', KEYS[1], quantity)

return {ok = 'SUCCESS'}
`
	_, err := global.REDIS.Eval(ctx, script, []string{cacheKey}, []string{util.StrVal(exchangeQty)}).Result()
	if err != nil {
		return err
	}

	return nil
}

// RetryExchangeWithBackoff 退避重试兑换
func RetryExchangeWithBackoff(ctx context.Context, req *ycopendefine.BonusExchangeWithdrawOrderReq) {
	maxRetries := 3
	delayTime := time.Second * 5 // 初始等待时间为 5 秒，后续按倍数增加
	var finalErr error
	for i := 0; i < maxRetries; i++ {
		time.Sleep(delayTime)
		orderData, result, err := yc_open.CreateBonusExchangeOrder(ctx, req)
		if result == yc_open.BonusExchangeLowStocks {
			// 库存不足
			handleExchangeFailed(ctx, req, "库存不足，积分已退回")
			return
		} else if result == yc_open.BonusExchangeFailed {
			// 明确失败
			handleExchangeFailed(ctx, req, "兑换失败，积分已退回")
			return
		} else if result == yc_open.BonusExchangeSuccess {
			// 明确成功
			handleExchangeSuccess(ctx, req, orderData)
			return
		}

		delayTime = delayTime * 2
		finalErr = err
	}

	// 重试后仍无法成功，触发告警
	option := &commondefine.WarnMsgMentionOption{
		MentionedMobileList: []string{"15921993416"},
	}
	commonFacade.SendDefaultWarnMsgWithMentionOption(ctx, "【积分商城-重试后仍兑换失败】",
		fmt.Sprintf("兑换记录Id: %+v, 错误信息: %v", req.ExchangeLogId, finalErr), option)
}

// handleExchangeFailed 兑换失败后续处理
func handleExchangeFailed(ctx context.Context, req *ycopendefine.BonusExchangeWithdrawOrderReq, failedReason string) {
	logPrefix := "[handleExchangeFailed]"
	exLogID, _ := util.Str2Int64(req.ExchangeLogId)
	exLogSchema := repo.GetQuery().BonusItemExchangeLog
	exLogQb := search.NewQueryBuilder().
		Eq(exLogSchema.BonusItemExchangeLogID, exLogID).
		Eq(exLogSchema.Status, enums.ExchangeLogStatusPending.Val()).
		Build()
	exchangeLog, err := repo.NewBonusItemExchangeLogRepo(exLogSchema.WithContext(ctx)).SelectOne(exLogQb)
	warnOption := &commondefine.WarnMsgMentionOption{
		MentionedMobileList: []string{"15921993416"},
	}
	if err != nil {
		log.Ctx(ctx).Errorf(logPrefix+" 获取兑换记录错误：%v", err)
		// 触发告警
		commonFacade.SendDefaultWarnMsgWithMentionOption(ctx, "【积分商城-兑换失败后续处理获取兑换记录错误】",
			fmt.Sprintf("兑换记录Id: %+v, 错误信息: %v", req.ExchangeLogId, err), warnOption)
		return
	}
	// 事务执行
	err = repo.ExecGenTx(ctx, func(ctx context.Context) error {
		// 更新兑换记录
		extra, _ := json.Marshal(req)
		updateExLogParams := map[string]interface{}{
			"status":        enums.ExchangeLogStatusFailed.Val(),
			"extra":         extra,
			"failed_reason": failedReason,
		}
		txErr := repo.NewBonusItemExchangeLogRepo(exLogSchema.WithContext(ctx)).UpdateField(updateExLogParams, exLogQb)
		if txErr != nil {
			return txErr
		}
		// 返还用户积分
		walletSchema := assetrepo.GetQuery().UserWallet
		queryWrapper := search.NewQueryBuilder().Eq(walletSchema.UserID, exchangeLog.UserID).Build()
		update := map[string]interface{}{
			"bonus": gorm.Expr("bonus + ?", exchangeLog.BonusTotal),
		}
		txErr = assetrepo.NewUserWalletRepo(walletSchema.WithContext(ctx)).UpdateField(update, queryWrapper)
		if txErr != nil && !errors.Is(txErr, gorm.ErrRecordNotFound) {
			return txErr
		}
		// 记录积分消费明细
		bonusLogSchema := assetrepo.GetQuery().UserBonusLog
		userBonusLog := &assetmodel.UserBonusLog{
			UserID:        exchangeLog.UserID,
			Name:          exchangeLog.ItemName,
			MainImg:       exchangeLog.IconURL,
			Source:        "兑换失败退回",
			Amount:        exchangeLog.BonusTotal,
			RelateID:      util.StrVal(exchangeLog.BonusItemExchangeLogID),
			ReceiveStatus: assetenum.BonusStatusReceived.Val(),
			CreatedAt:     util.Now(),
		}
		txErr = assetrepo.NewUserBonusLogRepo(bonusLogSchema.WithContext(ctx)).Save(userBonusLog)
		if txErr != nil && !util.IsMySQLDuplicateError(txErr) {
			return txErr
		}

		return nil
	})

	if err != nil {
		log.Ctx(ctx).Errorf(logPrefix+" 兑换失败后续处理错误：%v", err)
		// 触发告警
		commonFacade.SendDefaultWarnMsgWithMentionOption(ctx, "【积分商城-兑换失败后续处理错误】",
			fmt.Sprintf("兑换记录Id: %+v, 错误信息: %v", req.ExchangeLogId, err), warnOption)
		return
	}

	// 回退用户限制
	bonusItemSchema := repo.GetQuery().BonusItem
	bonusItemQb := search.NewQueryBuilder().
		Eq(bonusItemSchema.BonusItemID, exchangeLog.BonusItemID).
		Build()
	bonusItem, err := repo.NewBonusItemRepo(bonusItemSchema.WithContext(ctx)).SelectOne(bonusItemQb)
	if err != nil {
		log.Ctx(ctx).Errorf(logPrefix+" get bonus item error: %v", err)
		commonFacade.SendDefaultWarnMsgWithMentionOption(ctx, "【积分商城-兑换失败后续处理错误】",
			fmt.Sprintf("兑换记录Id: %+v, 错误信息: %v", req.ExchangeLogId, err), warnOption)
		return
	}
	err = RollbackUserLimit(ctx, bonusItem, exchangeLog.UserID, exchangeLog.ExchangeQty)
	if err != nil {
		log.Ctx(ctx).Errorf(logPrefix+" rollback user limit error: %v", err)
		commonFacade.SendDefaultWarnMsgWithMentionOption(ctx, "【积分商城-兑换失败后续处理错误】",
			fmt.Sprintf("兑换记录Id: %+v, 错误信息: %v", req.ExchangeLogId, err), warnOption)
		return
	}
}

// handleExchangeSuccess 兑换成功后续处理
func handleExchangeSuccess(ctx context.Context, req *ycopendefine.BonusExchangeWithdrawOrderReq, orderData *ycopendefine.BonusExchangeWithdrawOrderInfo) {
	logPrefix := "[handleExchangeSuccess]"
	exLogID, _ := util.Str2Int64(req.ExchangeLogId)
	exLogSchema := repo.GetQuery().BonusItemExchangeLog
	exLogQb := search.NewQueryBuilder().
		Eq(exLogSchema.BonusItemExchangeLogID, exLogID).
		Eq(exLogSchema.Status, enums.ExchangeLogStatusPending.Val()).
		Build()
	extra, _ := json.Marshal(req)
	updateExLogParams := map[string]interface{}{
		"status":               enums.ExchangeLogStatusSuccess.Val(),
		"yc_withdraw_order_id": orderData.ItemWithdrawOrderID,
		"extra":                extra,
	}
	err := repo.NewBonusItemExchangeLogRepo(exLogSchema.WithContext(ctx)).UpdateField(updateExLogParams, exLogQb)
	if err != nil {
		log.Ctx(ctx).Errorf(logPrefix+" 兑换成功后续处理错误：%v", err)
		// 触发告警
		option := &commondefine.WarnMsgMentionOption{
			MentionedMobileList: []string{"15921993416"},
		}
		commonFacade.SendDefaultWarnMsgWithMentionOption(ctx, "【积分商城-兑换成功后续处理错误】",
			fmt.Sprintf("兑换记录Id: %+v, 错误信息: %v", req.ExchangeLogId, err), option)
	}
}

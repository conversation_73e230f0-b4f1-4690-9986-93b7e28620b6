package locker

import (
	"fmt"
	"time"
)

// BonusItemAction 积分商品
type BonusItemAction string

const (
	Exchange BonusItemAction = "exchange" // 兑换商品
)

type BonusItemLock struct {
	ac  BonusItemAction // 行为
	tag string          // 唯一标识
}

func (p *BonusItemLock) GetCacheKey() string {
	return fmt.Sprintf("app_service:bonus_item:locker:%s:%s", p.ac, p.tag)
}

func (p *BonusItemLock) LockTime() time.Duration {
	return time.Second * 10
}

func NewBonusItemLock(tag string, ac BonusItemAction) *BonusItemLock {
	return &BonusItemLock{tag: tag, ac: ac}
}

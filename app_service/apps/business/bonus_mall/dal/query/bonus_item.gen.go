// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/bonus_mall/dal/model"
)

func newBonusItem(db *gorm.DB, opts ...gen.DOOption) bonusItem {
	_bonusItem := bonusItem{}

	_bonusItem.bonusItemDo.UseDB(db, opts...)
	_bonusItem.bonusItemDo.UseModel(&model.BonusItem{})

	tableName := _bonusItem.bonusItemDo.TableName()
	_bonusItem.ALL = field.NewAsterisk(tableName)
	_bonusItem.BonusItemID = field.NewInt64(tableName, "bonus_item_id")
	_bonusItem.SteamItemID = field.NewString(tableName, "steam_item_id")
	_bonusItem.SkuNo = field.NewString(tableName, "sku_no")
	_bonusItem.ItemName = field.NewString(tableName, "item_name")
	_bonusItem.IconURL = field.NewString(tableName, "icon_url")
	_bonusItem.Status = field.NewInt32(tableName, "status")
	_bonusItem.ExchangePrice = field.NewInt32(tableName, "exchange_price")
	_bonusItem.StockQty = field.NewInt32(tableName, "stock_qty")
	_bonusItem.ExchangedQty = field.NewInt32(tableName, "exchanged_qty")
	_bonusItem.ExchangeStartTime = field.NewTime(tableName, "exchange_start_time")
	_bonusItem.ExchangeEndTime = field.NewTime(tableName, "exchange_end_time")
	_bonusItem.PerUserLimitQty = field.NewInt32(tableName, "per_user_limit_qty")
	_bonusItem.PerUserLimitRefreshRate = field.NewString(tableName, "per_user_limit_refresh_rate")
	_bonusItem.ExchangeUserType = field.NewInt32(tableName, "exchange_user_type")
	_bonusItem.Priority = field.NewInt32(tableName, "priority")
	_bonusItem.CreatedBy = field.NewString(tableName, "created_by")
	_bonusItem.CreatedAt = field.NewTime(tableName, "created_at")
	_bonusItem.UpdatedAt = field.NewTime(tableName, "updated_at")

	_bonusItem.fillFieldMap()

	return _bonusItem
}

// bonusItem 积分商品表
type bonusItem struct {
	bonusItemDo

	ALL                     field.Asterisk
	BonusItemID             field.Int64  // 主键id
	SteamItemID             field.String // 实物商品id(tmt.steam_items._id)
	SkuNo                   field.String // 商品sku
	ItemName                field.String // 商品名称
	IconURL                 field.String // 商品图片（tmt.steam_item.icon_url）
	Status                  field.Int32  // 状态，0：待上架，1：已上架，2：已下架，3：已结束
	ExchangePrice           field.Int32  // 兑换价格（积分）
	StockQty                field.Int32  // 可兑换数量/库存
	ExchangedQty            field.Int32  // 已兑数量
	ExchangeStartTime       field.Time   // 可兑开始时间
	ExchangeEndTime         field.Time   // 可兑结束时间
	PerUserLimitQty         field.Int32  // 每人限兑数量，-1 表示不限
	PerUserLimitRefreshRate field.String // 每人限兑刷新频率，not_refresh: 不刷新，1d: 每天刷新
	ExchangeUserType        field.Int32  // 可兑人群，-1：不限
	Priority                field.Int32  // 优先级
	CreatedBy               field.String // 创建人id
	CreatedAt               field.Time   // 创建时间
	UpdatedAt               field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (b bonusItem) Table(newTableName string) *bonusItem {
	b.bonusItemDo.UseTable(newTableName)
	return b.updateTableName(newTableName)
}

func (b bonusItem) As(alias string) *bonusItem {
	b.bonusItemDo.DO = *(b.bonusItemDo.As(alias).(*gen.DO))
	return b.updateTableName(alias)
}

func (b *bonusItem) updateTableName(table string) *bonusItem {
	b.ALL = field.NewAsterisk(table)
	b.BonusItemID = field.NewInt64(table, "bonus_item_id")
	b.SteamItemID = field.NewString(table, "steam_item_id")
	b.SkuNo = field.NewString(table, "sku_no")
	b.ItemName = field.NewString(table, "item_name")
	b.IconURL = field.NewString(table, "icon_url")
	b.Status = field.NewInt32(table, "status")
	b.ExchangePrice = field.NewInt32(table, "exchange_price")
	b.StockQty = field.NewInt32(table, "stock_qty")
	b.ExchangedQty = field.NewInt32(table, "exchanged_qty")
	b.ExchangeStartTime = field.NewTime(table, "exchange_start_time")
	b.ExchangeEndTime = field.NewTime(table, "exchange_end_time")
	b.PerUserLimitQty = field.NewInt32(table, "per_user_limit_qty")
	b.PerUserLimitRefreshRate = field.NewString(table, "per_user_limit_refresh_rate")
	b.ExchangeUserType = field.NewInt32(table, "exchange_user_type")
	b.Priority = field.NewInt32(table, "priority")
	b.CreatedBy = field.NewString(table, "created_by")
	b.CreatedAt = field.NewTime(table, "created_at")
	b.UpdatedAt = field.NewTime(table, "updated_at")

	b.fillFieldMap()

	return b
}

func (b *bonusItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := b.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (b *bonusItem) fillFieldMap() {
	b.fieldMap = make(map[string]field.Expr, 18)
	b.fieldMap["bonus_item_id"] = b.BonusItemID
	b.fieldMap["steam_item_id"] = b.SteamItemID
	b.fieldMap["sku_no"] = b.SkuNo
	b.fieldMap["item_name"] = b.ItemName
	b.fieldMap["icon_url"] = b.IconURL
	b.fieldMap["status"] = b.Status
	b.fieldMap["exchange_price"] = b.ExchangePrice
	b.fieldMap["stock_qty"] = b.StockQty
	b.fieldMap["exchanged_qty"] = b.ExchangedQty
	b.fieldMap["exchange_start_time"] = b.ExchangeStartTime
	b.fieldMap["exchange_end_time"] = b.ExchangeEndTime
	b.fieldMap["per_user_limit_qty"] = b.PerUserLimitQty
	b.fieldMap["per_user_limit_refresh_rate"] = b.PerUserLimitRefreshRate
	b.fieldMap["exchange_user_type"] = b.ExchangeUserType
	b.fieldMap["priority"] = b.Priority
	b.fieldMap["created_by"] = b.CreatedBy
	b.fieldMap["created_at"] = b.CreatedAt
	b.fieldMap["updated_at"] = b.UpdatedAt
}

func (b bonusItem) clone(db *gorm.DB) bonusItem {
	b.bonusItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return b
}

func (b bonusItem) replaceDB(db *gorm.DB) bonusItem {
	b.bonusItemDo.ReplaceDB(db)
	return b
}

type bonusItemDo struct{ gen.DO }

type IBonusItemDo interface {
	gen.SubQuery
	Debug() IBonusItemDo
	WithContext(ctx context.Context) IBonusItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IBonusItemDo
	WriteDB() IBonusItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IBonusItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IBonusItemDo
	Not(conds ...gen.Condition) IBonusItemDo
	Or(conds ...gen.Condition) IBonusItemDo
	Select(conds ...field.Expr) IBonusItemDo
	Where(conds ...gen.Condition) IBonusItemDo
	Order(conds ...field.Expr) IBonusItemDo
	Distinct(cols ...field.Expr) IBonusItemDo
	Omit(cols ...field.Expr) IBonusItemDo
	Join(table schema.Tabler, on ...field.Expr) IBonusItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IBonusItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) IBonusItemDo
	Group(cols ...field.Expr) IBonusItemDo
	Having(conds ...gen.Condition) IBonusItemDo
	Limit(limit int) IBonusItemDo
	Offset(offset int) IBonusItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IBonusItemDo
	Unscoped() IBonusItemDo
	Create(values ...*model.BonusItem) error
	CreateInBatches(values []*model.BonusItem, batchSize int) error
	Save(values ...*model.BonusItem) error
	First() (*model.BonusItem, error)
	Take() (*model.BonusItem, error)
	Last() (*model.BonusItem, error)
	Find() ([]*model.BonusItem, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.BonusItem, err error)
	FindInBatches(result *[]*model.BonusItem, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.BonusItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IBonusItemDo
	Assign(attrs ...field.AssignExpr) IBonusItemDo
	Joins(fields ...field.RelationField) IBonusItemDo
	Preload(fields ...field.RelationField) IBonusItemDo
	FirstOrInit() (*model.BonusItem, error)
	FirstOrCreate() (*model.BonusItem, error)
	FindByPage(offset int, limit int) (result []*model.BonusItem, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IBonusItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (b bonusItemDo) Debug() IBonusItemDo {
	return b.withDO(b.DO.Debug())
}

func (b bonusItemDo) WithContext(ctx context.Context) IBonusItemDo {
	return b.withDO(b.DO.WithContext(ctx))
}

func (b bonusItemDo) ReadDB() IBonusItemDo {
	return b.Clauses(dbresolver.Read)
}

func (b bonusItemDo) WriteDB() IBonusItemDo {
	return b.Clauses(dbresolver.Write)
}

func (b bonusItemDo) Session(config *gorm.Session) IBonusItemDo {
	return b.withDO(b.DO.Session(config))
}

func (b bonusItemDo) Clauses(conds ...clause.Expression) IBonusItemDo {
	return b.withDO(b.DO.Clauses(conds...))
}

func (b bonusItemDo) Returning(value interface{}, columns ...string) IBonusItemDo {
	return b.withDO(b.DO.Returning(value, columns...))
}

func (b bonusItemDo) Not(conds ...gen.Condition) IBonusItemDo {
	return b.withDO(b.DO.Not(conds...))
}

func (b bonusItemDo) Or(conds ...gen.Condition) IBonusItemDo {
	return b.withDO(b.DO.Or(conds...))
}

func (b bonusItemDo) Select(conds ...field.Expr) IBonusItemDo {
	return b.withDO(b.DO.Select(conds...))
}

func (b bonusItemDo) Where(conds ...gen.Condition) IBonusItemDo {
	return b.withDO(b.DO.Where(conds...))
}

func (b bonusItemDo) Order(conds ...field.Expr) IBonusItemDo {
	return b.withDO(b.DO.Order(conds...))
}

func (b bonusItemDo) Distinct(cols ...field.Expr) IBonusItemDo {
	return b.withDO(b.DO.Distinct(cols...))
}

func (b bonusItemDo) Omit(cols ...field.Expr) IBonusItemDo {
	return b.withDO(b.DO.Omit(cols...))
}

func (b bonusItemDo) Join(table schema.Tabler, on ...field.Expr) IBonusItemDo {
	return b.withDO(b.DO.Join(table, on...))
}

func (b bonusItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) IBonusItemDo {
	return b.withDO(b.DO.LeftJoin(table, on...))
}

func (b bonusItemDo) RightJoin(table schema.Tabler, on ...field.Expr) IBonusItemDo {
	return b.withDO(b.DO.RightJoin(table, on...))
}

func (b bonusItemDo) Group(cols ...field.Expr) IBonusItemDo {
	return b.withDO(b.DO.Group(cols...))
}

func (b bonusItemDo) Having(conds ...gen.Condition) IBonusItemDo {
	return b.withDO(b.DO.Having(conds...))
}

func (b bonusItemDo) Limit(limit int) IBonusItemDo {
	return b.withDO(b.DO.Limit(limit))
}

func (b bonusItemDo) Offset(offset int) IBonusItemDo {
	return b.withDO(b.DO.Offset(offset))
}

func (b bonusItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IBonusItemDo {
	return b.withDO(b.DO.Scopes(funcs...))
}

func (b bonusItemDo) Unscoped() IBonusItemDo {
	return b.withDO(b.DO.Unscoped())
}

func (b bonusItemDo) Create(values ...*model.BonusItem) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Create(values)
}

func (b bonusItemDo) CreateInBatches(values []*model.BonusItem, batchSize int) error {
	return b.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (b bonusItemDo) Save(values ...*model.BonusItem) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Save(values)
}

func (b bonusItemDo) First() (*model.BonusItem, error) {
	if result, err := b.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.BonusItem), nil
	}
}

func (b bonusItemDo) Take() (*model.BonusItem, error) {
	if result, err := b.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.BonusItem), nil
	}
}

func (b bonusItemDo) Last() (*model.BonusItem, error) {
	if result, err := b.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.BonusItem), nil
	}
}

func (b bonusItemDo) Find() ([]*model.BonusItem, error) {
	result, err := b.DO.Find()
	return result.([]*model.BonusItem), err
}

func (b bonusItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.BonusItem, err error) {
	buf := make([]*model.BonusItem, 0, batchSize)
	err = b.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (b bonusItemDo) FindInBatches(result *[]*model.BonusItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return b.DO.FindInBatches(result, batchSize, fc)
}

func (b bonusItemDo) Attrs(attrs ...field.AssignExpr) IBonusItemDo {
	return b.withDO(b.DO.Attrs(attrs...))
}

func (b bonusItemDo) Assign(attrs ...field.AssignExpr) IBonusItemDo {
	return b.withDO(b.DO.Assign(attrs...))
}

func (b bonusItemDo) Joins(fields ...field.RelationField) IBonusItemDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Joins(_f))
	}
	return &b
}

func (b bonusItemDo) Preload(fields ...field.RelationField) IBonusItemDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Preload(_f))
	}
	return &b
}

func (b bonusItemDo) FirstOrInit() (*model.BonusItem, error) {
	if result, err := b.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.BonusItem), nil
	}
}

func (b bonusItemDo) FirstOrCreate() (*model.BonusItem, error) {
	if result, err := b.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.BonusItem), nil
	}
}

func (b bonusItemDo) FindByPage(offset int, limit int) (result []*model.BonusItem, count int64, err error) {
	result, err = b.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = b.Offset(-1).Limit(-1).Count()
	return
}

func (b bonusItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = b.Count()
	if err != nil {
		return
	}

	err = b.Offset(offset).Limit(limit).Scan(result)
	return
}

func (b bonusItemDo) Scan(result interface{}) (err error) {
	return b.DO.Scan(result)
}

func (b bonusItemDo) Delete(models ...*model.BonusItem) (result gen.ResultInfo, err error) {
	return b.DO.Delete(models)
}

func (b *bonusItemDo) withDO(do gen.Dao) *bonusItemDo {
	b.DO = *do.(*gen.DO)
	return b
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
)

const TableNameBonusItemExchangeLog = "bonus_item_exchange_log"

// BonusItemExchangeLog 积分商品兑换记录表
type BonusItemExchangeLog struct {
	BonusItemExchangeLogID int64           `gorm:"column:bonus_item_exchange_log_id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主健id" json:"bonus_item_exchange_log_id"` // 主健id
	BonusItemID            int64           `gorm:"column:bonus_item_id;type:bigint unsigned;not null;comment:积分商品id" json:"bonus_item_id"`                                              // 积分商品id
	SteamItemID            string          `gorm:"column:steam_item_id;type:char(24);not null;comment:实物商品id(tmt.steam_items._id)" json:"steam_item_id"`                                // 实物商品id(tmt.steam_items._id)
	SkuNo                  string          `gorm:"column:sku_no;type:varchar(64);not null;comment:商品sku" json:"sku_no"`                                                                 // 商品sku
	ItemName               string          `gorm:"column:item_name;type:varchar(256);not null;comment:商品名称" json:"item_name"`                                                           // 商品名称
	IconURL                string          `gorm:"column:icon_url;type:varchar(256);comment:商品图片（tmt.steam_item.icon_url）" json:"icon_url"`                                             // 商品图片（tmt.steam_item.icon_url）
	UserID                 string          `gorm:"column:user_id;type:char(24);not null;comment:用户id(pat.users._id)" json:"user_id"`                                                    // 用户id(pat.users._id)
	MobilePhone            string          `gorm:"column:mobile_phone;type:varchar(16);comment:用户手机号" json:"mobile_phone"`                                                              // 用户手机号
	Nickname               string          `gorm:"column:nickname;type:varchar(256);comment:用户昵称" json:"nickname"`                                                                      // 用户昵称
	UserAvatar             string          `gorm:"column:user_avatar;type:varchar(256);comment:用户头像" json:"user_avatar"`                                                                // 用户头像
	ExchangeTime           time.Time       `gorm:"column:exchange_time;type:datetime;not null;comment:兑换时间" json:"exchange_time"`                                                       // 兑换时间
	ExchangePrice          int32           `gorm:"column:exchange_price;type:int;not null;comment:兑换价格（积分）" json:"exchange_price"`                                                      // 兑换价格（积分）
	ExchangeQty            int32           `gorm:"column:exchange_qty;type:int;not null;comment:兑换数量" json:"exchange_qty"`                                                              // 兑换数量
	BonusTotal             int32           `gorm:"column:bonus_total;type:int;not null;comment:消耗积分" json:"bonus_total"`                                                                // 消耗积分
	CostTotal              int32           `gorm:"column:cost_total;type:int;not null;comment:合计成本（单位：分）" json:"cost_total"`                                                            // 合计成本（单位：分）
	Status                 int32           `gorm:"column:status;type:tinyint;not null;comment:状态，0：处理中，1：成功，2：失败" json:"status"`                                                        // 状态，0：处理中，1：成功，2：失败
	FailedReason           string          `gorm:"column:failed_reason;type:varchar(256);comment:失败原因" json:"failed_reason"`                                                            // 失败原因
	YcWithdrawOrderID      string          `gorm:"column:yc_withdraw_order_id;type:char(24);comment:云仓订单 id（item_withdraw_orders._id）" json:"yc_withdraw_order_id"`                     // 云仓订单 id（item_withdraw_orders._id）
	Extra                  *datatypes.JSON `gorm:"column:extra;type:json;comment:额外信息" json:"extra"`                                                                                    // 额外信息
	CreatedAt              time.Time       `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                                   // 创建时间
	UpdatedAt              time.Time       `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                                   // 更新时间
}

// TableName BonusItemExchangeLog's table name
func (*BonusItemExchangeLog) TableName() string {
	return TableNameBonusItemExchangeLog
}

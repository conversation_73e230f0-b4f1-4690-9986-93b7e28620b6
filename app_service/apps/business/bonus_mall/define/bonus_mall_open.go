package define

type (
	FinishBonusItemReq  struct{}
	FinishBonusItemResp struct{}
)

type (
	CheckAndAlarmAbnormalExchangeLogReq  struct{}
	CheckAndAlarmAbnormalExchangeLogResp struct {
		AbnormalExchangeLogIDs []int64 `json:"abnormal_exchange_log_ids"` // 异常兑换记录列表
		Total                  int64   `json:"total"`                     // 异常兑换记录总数
	}
)

type (
	ClearExpiredExchangeLimitCacheReq  struct{}
	ClearExpiredExchangeLimitCacheResp struct{}
)

package define

import (
	"app_service/apps/business/bonus_mall/dal/model"
	assetmodel "app_service/apps/platform/asset/dal/model"
	"app_service/apps/platform/issue/dal/model/mongdb"
	"app_service/pkg/pagination"
	"time"
)

type (
	GetExchangeLogWebTopListReq  struct{}
	GetExchangeLogWebTopListItem struct {
		NickName string `json:"nick_name"` // 用户昵称
		Avatar   string `json:"avatar"`    // 用户头像
		ItemName string `json:"item_name"` // 商品名称
	}
	GetExchangeLogWebTopListResp struct {
		List []*GetExchangeLogWebTopListItem `json:"list"`
	}
)

type (
	GetBonusItemWebListReq struct {
		pagination.Pagination
	}
	GetBonusItemWebListInfo struct {
		ID                int64     `json:"id,string"`
		IconURL           string    `json:"icon_url"`            // 商品主图
		ItemName          string    `json:"item_name"`           // 商品名称
		ExchangeStartTime time.Time `json:"exchange_start_time"` // 兑换开始时间
		ExchangeEndTime   time.Time `json:"exchange_end_time"`   // 兑换结束时间
		ExchangePrice     int32     `json:"exchange_price"`      // 兑换价格（积分）
		SellPrice         int32     `json:"sell_price"`          // 售价（单位：分）
		StockQty          int32     `json:"stock_qty"`           // 库存
		Status            int32     `json:"status"`              // 状态，0：待上架，1：已上架，2：已下架，3：已结束
		CurrentTime       string    `json:"current_time"`        // 系统当前时间
	}
	GetBonusItemWebListResp struct {
		List  []*GetBonusItemWebListInfo `json:"list"`
		Total int64                      `json:"total"`
	}
)

type (
	GetBonusItemWebDetailReq struct {
		ID int64 `form:"id" json:"id,string" binding:"required"`
	}
	GetBonusItemWebDetailResp struct {
		ID                      int64     `json:"id,string"`
		IconURL                 string    `json:"icon_url"`                    // 主图
		ImageInfos              []string  `json:"image_infos"`                 // 展示图
		ItemName                string    `json:"item_name"`                   // 商品名称
		DetailH5                string    `json:"detail_h5"`                   // 客户端详情
		SellPrice               int32     `json:"sell_price"`                  // 出售价格（单位：分）
		ExchangePrice           int32     `json:"exchange_price"`              // 兑换价格（积分）
		StockQty                int32     `json:"stock_qty"`                   // 库存量
		ExchangeStartTime       time.Time `json:"exchange_start_time"`         // 兑换开始时间（RFC3339格式）
		ExchangeEndTime         time.Time `json:"exchange_end_time"`           // 兑换结束时间（RFC3339格式）
		PerUserLimitQty         int32     `json:"per_user_limit_qty"`          // 用户限兑数量（-1表示不限量）
		PerUserLimitRefreshRate string    `json:"per_user_limit_refresh_rate"` // 限兑刷新频率枚举值
		CurrentTime             string    `json:"current_time"`                // 服务器当前时间
		Status                  int32     `json:"status"`                      // 状态，0：待上架，1：已上架，2：已下架，3：已结束
	}
)

type (
	GetUserExchangeLogWebListReq struct {
		pagination.Pagination
	}
	GetUserExchangeLogWebListInfo struct {
		ID           int64     `json:"id,string"`
		ItemName     string    `json:"item_name"`     // 商品名称
		IconURL      string    `json:"icon_url"`      // 商品主图
		ExchangeTime time.Time `json:"exchange_time"` // 兑换时间
		ExchangeQty  int32     `json:"exchange_qty"`  // 兑换数量
		BonusTotal   int32     `json:"bonus_total"`   // 消耗积分
		Status       int32     `json:"status"`        // 状态，0：处理中，1：成功，2：失败
		FailedReason string    `json:"failed_reason"` // 兑换失败原因
	}
	GetUserExchangeLogWebListResp struct {
		List  []*GetUserExchangeLogWebListInfo `json:"list"`
		Total int64                            `json:"total"`
	}
)

type (
	UserExchangeBonusItemReq struct {
		BonusItemID int64  `json:"bonus_item_id,string" binding:"required"` // 积分商品 id/活动 id
		ExchangeQty int32  `json:"exchange_qty" binding:"required,min=1"`   // 兑换数量
		AddressID   string `json:"address_id" binding:"required"`           // 地址 id
	}
	UserExchangeBonusItemResp struct {
		ID           int64     `json:"id,string"`
		ExchangeTime time.Time `json:"exchange_time"`
	}
	UserExchangeBonusItemDeps struct {
		SteamItem  *mongdb.SteamItem
		Address    *mongdb.Address
		BonusItem  *model.BonusItem
		UserWallet *assetmodel.UserWallet
	}
)

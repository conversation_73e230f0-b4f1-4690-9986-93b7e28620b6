package define

import (
	"app_service/pkg/pagination"
	"time"
)

type (
	AddBonusItemReq struct {
		SteamItemID             string    `json:"steam_item_id" binding:"required"`                                    // 实物商品ID
		ExchangePrice           int32     `json:"exchange_price" binding:"required"`                                   // 兑换价格
		StockQty                int32     `json:"stock_qty" binding:"required,min=1"`                                  // 库存量
		ExchangeStartTime       time.Time `json:"exchange_start_time" binding:"required"`                              // 兑换开始时间（RFC3339格式）
		ExchangeEndTime         time.Time `json:"exchange_end_time" binding:"required"`                                // 兑换结束时间（RFC3339格式）
		PerUserLimitQty         int32     `json:"per_user_limit_qty" binding:"required,ne=0"`                          // 用户限兑数量（-1表示不限量）
		PerUserLimitRefreshRate string    `json:"per_user_limit_refresh_rate" binding:"required,oneof=not_refresh 1d"` // 限兑刷新频率，not_refresh：不刷新，1d：每天刷新
		ExchangeUserType        int32     `json:"exchange_user_type" binding:"required,oneof=-1"`                      // 可兑人群类型（-1表示不限）
		Priority                int32     `json:"priority" binding:"required,min=0,max=9999"`                          // 优先级数值，0-9999
	}
	AddBonusItemResp struct {
		ID int64 `json:"id,string"`
	}
)

type (
	EditBonusItemReq struct {
		ID                int64     `json:"id,string" binding:"required"`
		StockQty          int32     `json:"stock_qty" binding:"required,min=1"`             // 库存量
		ExchangeStartTime time.Time `json:"exchange_start_time" binding:"required"`         // 兑换开始时间（RFC3339格式）
		ExchangeEndTime   time.Time `json:"exchange_end_time" binding:"required"`           // 兑换结束时间（RFC3339格式）
		PerUserLimitQty   int32     `json:"per_user_limit_qty" binding:"required,ne=0"`     // 用户限兑数量（-1表示不限量）
		ExchangeUserType  int32     `json:"exchange_user_type" binding:"required,oneof=-1"` // 可兑人群类型，-1：不限
		Priority          int32     `json:"priority" binding:"required,min=0,max=9999"`     // 优先级数值，0-9999
	}
	EditBonusItemResp struct {
		ID int64 `json:"id,string"`
	}
)

type (
	EditBonusItemStatusReq struct {
		ID     int64 `json:"id,string" binding:"required"`
		Status int32 `json:"status" binding:"required,oneof=1 2"` // 状态，1：已上架，2：已下架
	}
	EditBonusItemStatusResp struct {
		ID int64 `json:"id,string"`
	}
)

type (
	GetBonusItemDetailAdminReq struct {
		ID int64 `form:"id" json:"id,string" binding:"required"`
	}
	GetBonusItemDetailAdminResp struct {
		ID                      int64     `json:"id,string"`
		SteamItemID             string    `json:"steam_item_id"`               // 实物商品ID
		ExchangePrice           int32     `json:"exchange_price"`              // 兑换价格
		StockQty                int32     `json:"stock_qty"`                   // 库存量
		ExchangeStartTime       time.Time `json:"exchange_start_time"`         // 兑换开始时间（RFC3339格式）
		ExchangeEndTime         time.Time `json:"exchange_end_time"`           // 兑换结束时间（RFC3339格式）
		PerUserLimitQty         int32     `json:"per_user_limit_qty"`          // 用户限兑数量（-1表示不限量）
		PerUserLimitRefreshRate string    `json:"per_user_limit_refresh_rate"` // 限兑刷新频率枚举值
		ExchangeUserType        int32     `json:"exchange_user_type"`          // 可兑人群类型
		Priority                int32     `json:"priority"`                    // 优先级
		Status                  int32     `json:"status"`                      // 状态，0：待上架，1：已上架，2：已下架，3：已结束
	}
)

type (
	GetBonusItemListAdminReq struct {
		pagination.Pagination
		StartTime   time.Time `form:"start_time"`
		EndTime     time.Time `form:"end_time"`
		ID          int64     `form:"id" json:"id,string"`         // 积分商品 id/活动 id
		ItemKeyword string    `form:"item_keyword"`                // 商品搜索关键词
		SkuNo       string    `form:"sku_no"`                      // 商品 sku
		Status      *int32    `form:"status" json:"status,string"` // 状态，0：待上架，1：已上架，2：已下架，3：已结束
	}
	BonusItemListAdminInfo struct {
		ID                int64     `json:"id,string"`
		SteamItemID       string    `json:"steam_item_id"`       // 实物商品ID
		ExchangePrice     int32     `json:"exchange_price"`      // 兑换价格
		StockQty          int32     `json:"stock_qty"`           // 库存量
		ExchangeStartTime time.Time `json:"exchange_start_time"` // 兑换开始时间（RFC3339格式）
		ExchangeEndTime   time.Time `json:"exchange_end_time"`   // 兑换结束时间（RFC3339格式）
		PerUserLimitQty   int32     `json:"per_user_limit_qty"`  // 用户限兑数量（-1表示不限量）
		Priority          int32     `json:"priority"`            // 优先级
		Status            int32     `json:"status"`              // 状态，0：待上架，1：已上架，2：已下架，3：已结束
		SkuNo             string    `json:"sku_no"`              // 商品 sku
		ItemName          string    `json:"item_name"`           // 商品名称
		IconURL           string    `json:"icon_url"`            // 商品主图
		ExchangedQty      int32     `json:"exchanged_qty"`       // 已兑换数量
	}
	GetBonusItemListAdminResp struct {
		List  []*BonusItemListAdminInfo `json:"list"`
		Total int64                     `json:"total"`
	}
)

type (
	GetExchangeLogListAdminReq struct {
		pagination.Pagination
		StartTime   time.Time `form:"start_time"`
		EndTime     time.Time `form:"end_time"`
		BonusItemID int64     `form:"bonus_item_id" json:"id,string"` // 积分商品 id/活动 id
		ItemKeyword string    `form:"item_keyword"`                   // 商品搜索关键词
		UserID      string    `form:"user_id"`                        // 用户 id
		MobilePhone string    `form:"mobile_phone"`                   // 用户手机号
		SkuNo       string    `form:"sku_no"`                         // 商品 sku
	}
	ExchangeLogAdminInfo struct {
		ID                int64     `json:"id,string"`
		BonusItemID       int64     `json:"bonus_item_id,string"` // 积分商品 id/活动 id
		ItemName          string    `json:"item_name"`            // 商品名称
		SteamItemID       string    `json:"steam_item_id"`        // 实物商品 id
		SkuNo             string    `json:"sku_no"`               // 商品 sku
		IconURL           string    `json:"icon_url"`             // 商品主图
		ExchangePrice     int32     `json:"exchange_price"`       // 兑换价格（积分）
		ExchangeTime      time.Time `json:"exchange_time"`        // 兑换时间
		ExchangeQty       int32     `json:"exchange_qty"`         // 兑换数量
		BonusTotal        int32     `json:"bonus_total"`          // 消耗积分
		CostTotal         int32     `json:"cost_total"`           // 成本合计（单位：分）
		UserID            string    `json:"user_id"`              // 用户 id
		MobilePhone       string    `json:"mobile_phone"`         // 用户手机号
		NickName          string    `json:"nick_name"`            // 用户昵称
		Status            int32     `json:"status"`               // 状态，0：处理中，1：成功，2：失败
		YcWithdrawOrderID string    `json:"yc_withdraw_order_id"` // 云仓发货单号
	}
	GetExchangeLogListAdminResp struct {
		List  []*ExchangeLogAdminInfo `json:"list"`
		Total int64                   `json:"total"`
	}
)

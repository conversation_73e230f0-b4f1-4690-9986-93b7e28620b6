package admin

import (
	"app_service/apps/business/bonus_mall/api/admin"
	"github.com/gin-gonic/gin"
)

// BonusMall 积分商城管理端相关路由
func BonusMall(router *gin.RouterGroup) {
	group := router.Group("/bonus_mall")
	{
		bonusItemGroup := group.Group("/bonus_item")
		// 新增积分商品
		bonusItemGroup.POST("/add", admin.AddBonusItem)
		// 修改积分商品
		bonusItemGroup.POST("/edit", admin.EditBonusItem)
		// 修改积分商品状态
		bonusItemGroup.POST("/edit_status", admin.EditBonusItemStatus)
		// 获取积分商品详情
		bonusItemGroup.GET("/detail", admin.GetBonusItemAdminDetail)
		// 获取积分商品列表
		bonusItemGroup.GET("/list", admin.GetBonusItemAdminList)
	}
	{
		exchangeLogGroup := group.Group("/exchange_log")
		// 获取兑换记录列表
		exchangeLogGroup.GET("/list", admin.GetExchangeLogAdminList)
		// 导出兑换记录列表
		exchangeLogGroup.GET("/list_export", admin.ExportExchangeLogList)
	}
}

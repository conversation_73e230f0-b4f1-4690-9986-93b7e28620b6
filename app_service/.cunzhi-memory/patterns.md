# 常用模式和最佳实践

- 先添加代码再添加导入，防止导入被自动删除
- 帖子管理业务逻辑已完成重构：1. 主要业务逻辑在 service 层实现（post_web.go, post_admin.go）2. 复用逻辑在 logic 层（merchant_permission.go）3. 使用 UserInfo 结构体统一用户信息格式 4. 通过 pat.GetRealInfo 获取真实用户信息（昵称、头像）5. API 层直接调用具体服务类，避免中间层
- SendMessage事务处理已优化：参考UserExchangeBonusItem的事务模式，使用tx.Create()直接操作而非repo方法；UpdateConversationLastMessage方法也已修改为使用tx.Model()直接操作，并增加了RowsAffected检查确保更新成功，提高了事务的一致性和错误处理能力
- 帖子编辑接口已添加：新增EditPost接口(/web/v1/posts/edit_content)，允许商家编辑帖子的描述、价格和媒体文件，包含完整的权限验证（商家身份、帖子所有权）和状态检查（违规下架和已删除的帖子不能编辑），编辑后自动清理相关缓存
- 帖子代码已优化复用logic层：新增ValidatePostParams、GetUserPostByID、ValidatePostEditableStatus等通用方法，CreatePost和EditPost方法已重构使用这些复用逻辑，减少代码重复，提高维护性
- 支付方式枚举定义：PaymentMethod枚举包含卡牌钱包、微信支付、支付宝三种支付方式，提供String()、IsValid()、Int32()、GetDisplayName()等标准方法，遵循项目枚举定义规范
- 订单列表接口优化完成：1.新增统一的GetOrderList接口，通过UserType参数区分买家(1)和卖家(2)视角；2.GetOrderListRequest增加UserType字段，支持user_type参数；3.保留原有GetMyBoughtOrders和GetMySoldOrders接口作为兼容性接口；4.新增路由/web/v1/orders/list作为统一入口；5.大幅减少重复代码，提高维护性
- 订单创建业务规则实现：1.修改FindExistingConversation返回*model.Conversation而非字符串；2.新增checkSellerUnpaidOrderLimit校验卖家待支付订单不超过5单；3.新增checkConversationStatus校验会话状态，会话不存在或状态为-1时不能创建订单；4.在CreateOrder中按顺序执行校验：买家有效性->卖家订单限制->会话状态->订单创建

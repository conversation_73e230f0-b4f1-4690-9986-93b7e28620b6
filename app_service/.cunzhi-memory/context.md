# 项目上下文信息

- 文潮集市卡牌集社IM系统设计：基于Figma分析发现智能回复功能有开启/关闭两种状态，用户端包含发布求购、我的发布等页面，需要设计消息列表页面实现用户与商家的一对一沟通，技术栈使用Go+Gin+GORM+MongoDB+Redis+Kafka
- card_community模块路由注册已完成：创建了主路由文件router.go，包含web端(会话、消息、帖子、商家申请、智能回复)和admin端(会话、消息、帖子、商家申请管理)路由注册；补充了缺失的admin/message.go路由文件；在init_router.go中添加了card_community路由导入
- 用户反馈：使用Apifox测试CreateApplication接口时，body放{}成功，什么都不放会出现EOF错误
- 用户要求实现完整的智能回复功能，包括模板管理和自动发送逻辑
- GenerateID函数提供接口给前端调用，用于生成Snowflake ID
- 缓存防护功能已完成实现：1)随机TTL防雪崩-帖子详情和列表缓存使用基础10分钟+0-10%随机时间 2)空值缓存防穿透-仅帖子详情使用2分钟空值缓存，列表不使用 3)新增pkg/cache/cache_util.go通用缓存工具类 4)修改apps/business/card_community/service/logic/post.go和post_web.go实现新缓存逻辑 5)提供test_cache_simple.sh和test_api_cache.sh测试脚本 

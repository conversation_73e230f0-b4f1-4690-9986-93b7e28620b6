package wat

import (
	"app_service/pkg/util"
	"context"
	"time"

	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

// SpdbCardBalancePayReq 余额支付请求参数
type SpdbCardBalancePayReq struct {
	BuyUserId  string `json:"buy_user_id"`  // 购买用户id
	SaleUserId string `json:"sale_user_id"` // 出售用户id
	Amount     int64  `json:"amount"`       // 金额/分
	OrderId    string `json:"order_id"`     // 订单号
}

// SpdbCardBalancePayDoneReq 余额支付完成请求参数
type SpdbCardBalancePayDoneReq struct {
	BuyUserId  string `json:"buy_user_id"`  // 购买用户id
	SaleUserId string `json:"sale_user_id"` // 出售用户id
	Amount     int64  `json:"amount"`       // 金额
	OrderId    string `json:"order_id"`     // 订单号
}

// SpdbCardPayStatusReq 查询支付状态请求参数
type SpdbCardPayStatusReq struct {
	BuyUserId string `json:"buy_user_id"` // 购买用户id
	OrderId   string `json:"order_id"`    // 订单号
}

// SpdbCardPayDoneStatusReq 查询支付完成状态请求参数
type SpdbCardPayDoneStatusReq struct {
	SaleUserId string `json:"sale_user_id"` // 出售用户id
	OrderId    string `json:"order_id"`     // 订单号
}

// SpdbPayData 支付接口返回的数据结构
type SpdbPayData struct {
	TrId string `json:"tr_id"` // 流水号
}

// SpdbPayResponse 浦发支付响应结构
type SpdbPayResponse struct {
	Code int32       `json:"code" form:"code"`
	Desc string      `json:"desc" form:"desc"`
	Data SpdbPayData `json:"data" form:"data"`
}

// SpdbPayStatusResponse 支付状态查询响应结构
type SpdbPayStatusResponse struct {
	Code int32  `json:"code" form:"code"`
	Desc string `json:"desc" form:"desc"`
	Data bool   `json:"data" form:"data"` // 是否支付成功 true:是;false:否
}

// SpdbCardBalancePay 余额支付(浦发卡牌钱包支付)
func SpdbCardBalancePay(ctx context.Context, form *SpdbCardBalancePayReq) (*SpdbPayData, error) {
	rsp := &SpdbPayResponse{}
	req, err := request.Wat()
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"buy_user_id":  form.BuyUserId,
		"sale_user_id": form.SaleUserId,
		"amount":       form.Amount,
		"order_id":     form.OrderId,
	}

	err = req.Call(
		ctx,
		"open/wallet/v1/spdb_card_balance_pay",
		rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithTimeOut(30*time.Second),
	)
	if err != nil {
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("余额支付异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return nil, response.Fail.SetMsg("余额支付失败")
	}

	if rsp.Data.TrId == "" {
		log.Ctx(ctx).Errorf("支付响应中未获取到流水号，返回数据：%v", util.Obj2JsonStr(rsp))
		return nil, response.Fail.SetMsg("余额支付失败")
	}

	return &rsp.Data, nil
}

// SpdbCardBalancePayDone 余额支付完成(买家确认收货，余额转入卖家)
func SpdbCardBalancePayDone(ctx context.Context, form *SpdbCardBalancePayDoneReq) (*SpdbPayData, error) {
	rsp := &SpdbPayResponse{}
	req, err := request.Wat()
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"buy_user_id":  form.BuyUserId,
		"sale_user_id": form.SaleUserId,
		"amount":       form.Amount,
		"order_id":     form.OrderId,
	}

	err = req.Call(
		ctx,
		"open/wallet/v1/spdb_card_balance_pay_done",
		rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithTimeOut(30*time.Second),
	)
	if err != nil {
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("余额支付完成异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return nil, response.Fail.SetMsg("余额支付完成失败")
	}
	//if rsp.Data.TrId == "" {
	//	log.Ctx(ctx).Errorf("余额支付完成响应中未获取到流水号，返回数据：%v", util.Obj2JsonStr(rsp))
	//	return nil, response.Fail.SetMsg("余额支付失败")
	//}

	return &rsp.Data, nil
}

// SpdbCardPayStatus 查询支付状态(浦发卡牌钱包支付)
func SpdbCardPayStatus(ctx context.Context, form *SpdbCardPayStatusReq) (bool, error) {
	rsp := &SpdbPayStatusResponse{}
	req, err := request.Wat()
	if err != nil {
		return false, err
	}

	params := map[string]interface{}{
		"buy_user_id": form.BuyUserId,
		"order_id":    form.OrderId,
	}

	err = req.Call(
		ctx,
		"open/wallet/v1/spdb_card_pay_status",
		rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodGet(),
		utilRequest.WithTimeOut(30*time.Second),
	)
	if err != nil {
		return false, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询支付状态异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return false, response.Fail.SetMsg("查询支付状态失败")
	}
	return rsp.Data, nil
}

// SpdbCardPayDoneStatus 查询支付状态(浦发卡牌钱包支付完成)
func SpdbCardPayDoneStatus(ctx context.Context, form *SpdbCardPayDoneStatusReq) (bool, error) {
	rsp := &SpdbPayStatusResponse{}
	req, err := request.Wat()
	if err != nil {
		return false, err
	}

	params := map[string]interface{}{
		"sale_user_id": form.SaleUserId,
		"order_id":     form.OrderId,
	}

	err = req.Call(
		ctx,
		"open/wallet/v1/spdb_card_pay_done_status",
		rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodGet(),
		utilRequest.WithTimeOut(30*time.Second),
	)
	if err != nil {
		return false, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询支付完成状态异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return false, response.Fail.SetMsg("查询支付完成状态失败")
	}
	return rsp.Data, nil
}

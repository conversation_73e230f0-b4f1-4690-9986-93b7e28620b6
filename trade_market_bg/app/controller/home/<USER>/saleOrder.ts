import {Controller} from 'egg';
import {
  BatchBuyRequestPartial,
  ItemWithdrawOrderPartial,
  SaleOrderEntity,
  SaleOrderPartial,
  SaleOrderQuery,
  UserItemLikesQuery
} from 'ExEntitys';
import {
  convertSorts,
  filingQuerysByMongo,
  filingSortsByMongo,
  SearchField,
  SearchTypeEnum,
  setObjByDotKeys,
  urlParse,
  UrlParseSearchOp
} from 'utils/repository';
import * as _ from 'lodash';
import ItemValidator from 'validator/home/<USER>/item';
import {ItemStatusEnum} from 'enum/item/item_status';
import {ObjectId, ObjectID} from 'bson';
import {ItemSaleStatusEnum} from 'enum/item/item_sale_status';
import {LockEnum} from 'enum/item/lock';
import {ItemExamineStatusEnum} from 'enum/item/item_examine_status';
import {ItemWithdrawOrderStatusEnum} from 'enum/user_item/item_withdraw_order_status';
import {OrderTypeEnum} from 'enum/order/order_type';
import ExError from 'utils/ex-error/ex_error';
import {UserTypeEnum} from 'enum/user/user_type';
import {UserForkStatusEnum} from 'enum/user/user_fork';
import {StatusEnum} from 'enum/status';
import {ReadPreference} from 'mongodb';
import {SaleOrderTypeEnum} from 'enum/item/sale_order_type';
import {UserItemStatusEnum} from 'enum/user_item/user_item_status';
import * as moment from 'moment';
import {enableBoxOfWxmini} from 'utils/item_order';
import {PlatformSourceEnum} from 'enum/order/platform_source';
import {Types} from "mongoose";
import {AppidEnum} from 'enum/item/appid';
import {UpChainStatusEnum} from 'enum/nft/up_chain_status';
import {RechargeMethodEnum} from 'enum/item/recharge_method';
import {getRedisLock} from 'utils/redis_helper';
import {Message, TopicMessages} from 'kafkajs';
import {maskNickname, randomWord} from 'utils/string';
import {SaleModeEnum} from 'enum/item/issue_item';

export default class SaleOrderController extends Controller {
  public logPrefix = '[controller.admin.item.saleOrder]';
  private validator: ItemValidator;

  constructor(ctx) {
    super(ctx);
    this.validator = new ItemValidator(ctx);
  }

  public async sale_order_creat() {
    const { ctx } = this;
    if (ctx.state.user.type === UserTypeEnum.Promoter) {
      throw new ExError('TMT_FAIL');
    }

    await this.validator.create();

    const form: SaleOrderPartial = ctx.state.form;
    // ctx.logger.info(`${this.logPrefix} create`, form);
    // 生成上架中状态的订单
    await ctx.service.user.user.checkUserCertifiedAndHasAddress();
    const saleOrder = await ctx.service.item.saleOrder.create(form);
    const user = ctx.state.user;
    // 藏品加锁 / 物品变更出售状态
    switch (form.order_type) {
      case SaleOrderTypeEnum.Nft: {
        for (let count = 0; count <= 2; count++) {
          try {
            // 重试三次后失败直接告警
            const lock = await ctx.service.airmartOpm.lock(form.nft_item_id, user.open_info.open_user_id);
            if (!lock) {
              throw new ExError('TMT_LOCK');
            }
            const updateForm = {
              status: ItemStatusEnum.sell,
            };
            await ctx.service.item.item.update({ _id: { $eq: form.nft_item_id } }, updateForm);
            break;
          } catch (error) {
            if (count >= 2) {
              ctx.service.morBg.saleErrWarn(OrderTypeEnum.Trade, saleOrder._id, form.nft_item_id);
              throw new ExError('TMT_FAIL', 'updateAirmartUserItemStatus err');
            }
          }
        }

        break;
      }
      case SaleOrderTypeEnum.UserItem: {
        // 重试三次后失败直接告警
        for (let count = 0; count <= 2; count++) {
          try {
            await ctx.service.item.userItem.update({ _id: { $eq: form.user_item_id }, status: UserItemStatusEnum.Owned, }, { $set: { status: UserItemStatusEnum.Saleing } });
            await ctx.service.item.userItem.updateYcUserItemStatus(form.user_item_id, UserItemStatusEnum.Owned, UserItemStatusEnum.Saleing);
            const saleOrder = await ctx.service.item.saleOrder.queryJustOne({ user_item_id: form.user_item_id, order_status: ItemSaleStatusEnum.sell });
            if (saleOrder) {
              throw new ExError('TMT_FAIL', 'updateAirmartUserItemStatus err');
            }
            break;
          } catch (error) {
            if (count >= 2) {
              ctx.service.morBg.saleErrWarn(OrderTypeEnum.UserItem, saleOrder._id, form.user_item_id);
              throw new ExError('TMT_FAIL', 'updateAirmartUserItemStatus err');
            }
          }

        }
        break;
      }
      default: break;
    }
    let query;
    if (saleOrder.order_type === SaleOrderTypeEnum.Nft) {
      query = {
        nft_item_id: saleOrder.nft_item_id,
        order_status: {
          $in: [
            ItemSaleStatusEnum.sell,
            ItemSaleStatusEnum.selling,
          ],
        },
      };
    } else {
      query = {
        user_item_id: saleOrder.user_item_id,
        order_status: {
          $in: [
            ItemSaleStatusEnum.sell,
            ItemSaleStatusEnum.selling,
            ItemSaleStatusEnum.sellOut,
          ],
        },
      };
    }
    const checkSaleOrder = await ctx.service.item.saleOrder.queryJustOne(query);
    if (!checkSaleOrder) {
      const sale_order_status = ItemSaleStatusEnum.sell;
      // 更新销售订单表
      await ctx.service.item.saleOrder.update({
        _id: saleOrder._id,
      }, {
        order_status: sale_order_status,
      });
      saleOrder.order_status = sale_order_status;
      // 自动过审的气仓商品订单才会进入整合表
      if (saleOrder.examine_status === ItemExamineStatusEnum.allow && saleOrder.order_type === SaleOrderTypeEnum.UserItem) {
        //挂单，修改挂单时间
        await ctx.service.match.addMatch(saleOrder);
      }
      if (ctx.state.is_order_info) {
        ctx.state.saleOrderId = saleOrder._id;
      }

      // 神策上报上架成功
      ctx.service.item.saleOrder.entityMerchandiseShelf(saleOrder);
      ctx.body = {
        code: 0,
        desc: '',
        data: saleOrder,
      };
    }
  }

  public async sale_order_creat_batch() {
    const { ctx, app } = this;
    const sellPwdInfo = await ctx.service.watBg.checkSellPwdInfo(ctx.state.user._id);
    if (sellPwdInfo.need_to_input_pwd) {
      const sellPwd = _.get(ctx.request.body, 'sell_pwd');
      if (!sellPwd) {
        // 应该输入密码，但是前端没有传过来，则拒绝请求
        throw new ExError('TMT_VALIDATION_FAILED', '请输入出售密码');
      }
      // 校验出售密码
      await ctx.service.watBg.verifySellPwd({
        sell_pwd: sellPwd,
        user_id: ctx.state.user._id,
      });
    }

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      throw new ExError('TMT_YC_DOWN');
    }
    // const allowSale = ctx.service.item.saleOrder.allowSale();
    // if (!allowSale) {
    //   const marketTimeConf = _.get(ctx.app.config.custom, 'market_time', {});
    //   throw new ExError('TMT_CURRENCY_ERROR', '', { message: `仅交易时间${marketTimeConf.start}~${marketTimeConf.end}可挂单出售` });
    // }
    await ctx.service.user.user.checkUserCertifiedAndHasAddress();
    const saleOrders = _.get(ctx.request.body, 'sale_orders', []);
    ctx.state.isUseReferencePrice = _.get(ctx.request.body, 'is_use_reference_price', false);
    ctx.state.isBulkSale = saleOrders.length > 1;
    ctx.state.is_order_info = _.get(ctx.request.body, 'is_order_info', false);
    let errSale: any = [];
    if (ctx.state.user.type === UserTypeEnum.FMBusinesser) {
      ctx.state.business_order_id = new ObjectID();
    } else if (ctx.state.user.type === UserTypeEnum.Promoter) {
      throw new ExError('TMT_FAIL');
    } else {
      ctx.state.saleBatchId = new ObjectID();
    }
    let errFlag = false;
    for (const saleOrder of saleOrders) {
      if (!saleOrder.order_amount || saleOrder.order_amount <= 0) {
        throw new ExError('TMT_FAIL');
      }
    }
    for (const saleOrder of saleOrders) {
      ctx.request.body = saleOrder;
      const userItem = await ctx.service.item.userItem.queryUserItem({
        _id:saleOrder._id
      })
      if(!userItem){
        ctx.logger.info(`saleOrder--userItem error:`,saleOrder._id);
        throw new ExError('TMT_ITEM_NOT_ISSUE');
      }
      try {
        await this.sale_order_creat();
      } catch (err) {
        if (err && _.get(err, 'code') === 85501232) {
          errFlag = true;
        }
        ctx.logger.error(`sale_order_creat_batch fail ${err}, ${saleOrder._id}`);
        ctx.logger.error(`sale_order_creat_batch fail ${JSON.stringify(err)}, ${saleOrder._id}`);
        errSale.push(saleOrder._id);
      }
    }

    // 如果是单件商品并且是虚拟卡商品则直接抛出错误
    if (saleOrders?.length === 1 && errFlag) {
      throw new ExError('TMT_CLOSE_VIRTUAL_SELL');
    }
    if (ctx.state.is_order_info) {
      if (errSale.length > 0) {
        errSale = [];
      } else {
        errSale.push(ctx.state.saleOrderId);
      }
    }
    ctx.body = {
      code: 0,
      desc: '',
      data: errSale,
    };
  }

  public async sale_order_update() {
    const { ctx } = this;
    await this.validator.update();
    const form = ctx.state.form;
    const saleOrder = ctx.state.saleOrder;
    const isUserItem = ctx.state.isUserItem;

    // 取消出售
    // 更改状态
    if (isUserItem) {
      const saleOrderUpdateFrom = {
        examine_status: ItemExamineStatusEnum.allow,
        order_status: ItemSaleStatusEnum.close,
        operation_id: ctx.state.user._id,
      };

      await ctx.service.item.saleOrder.update({
        _id: saleOrder._id,
      }, saleOrderUpdateFrom);
      // if (!saleOrder){
      //   throw new ExError('TMT_DATA_EXISTED', 'the saleOrder is exist');
      // }
      setObjByDotKeys(saleOrder, saleOrderUpdateFrom);
      await ctx.service.match.addMatch(saleOrder);
      // 神策上报 实物-商品取消出售
      ctx.service.item.saleOrder.entityCancellationGoods(saleOrder);
    } else {
      // const item = await ctx.service.item.item.update({ _id: { $eq: form._id }}, updateForm);
      // if (!item){
      //   throw new ExError('TMT_DATA_EXISTED', 'the item is exist');
      // }
      // 关闭订单
      const saleOrderUpdateFrom = {
        examine_status: ItemExamineStatusEnum.allow,
        order_status: ItemSaleStatusEnum.close,
        operation_id: ctx.state.user._id,
      };
      await ctx.service.item.saleOrder.update({
        nft_item_id: { $eq: form._id },
        order_status: ItemSaleStatusEnum.sell,
      }, saleOrderUpdateFrom);

      for (let count = 0; count <= 2; count++) {
        try {
          // 重试三次后失败直接告警
          const lock = await ctx.service.airmartOpm.unlock(form._id);
          if (!lock) {
            throw new ExError('TMT_LOCK');
          }
          const updateForm = {
            status: ItemStatusEnum.hold,
            lock: LockEnum.UnLock,
          };
          await ctx.service.item.item.update({ _id: { $eq: form._id } }, updateForm);
          break;
        } catch (error) {
          if (count >= 2) {
            const orderType = saleOrder?.order_type === SaleOrderTypeEnum.Nft ? OrderTypeEnum.Trade : OrderTypeEnum.UserItem;
            ctx.service.morBg.saleErrWarn(orderType, saleOrder._id, form._id, false);
            throw new ExError('TMT_FAIL', 'updateAirmartUserItemStatus err');
          }
        }
      }

    }

    ctx.body = {
      code: 0,
      desc: '',
      data: saleOrder._id,
    };

  }

  // 批量取消出售订单
  public async sale_order_update_batch() {
    const { ctx } = this;
    // 校验批量输入
    await this.validator.updateBatch();
    const saleOrders = ctx.state.saleOrders;
    // const user = ctx.state.user;
    // const form = ctx.state.form;
    const { _ids: ids } = ctx.request.body; // 解构批量入参
    if (!Array.isArray(ids) || ids.length === 0) {
      ctx.logger.error(`${this.logPrefix} saleOrderUpdateBatch saleOrder TMT_OPEN_API_PARAM_ERROR:${JSON.stringify(saleOrders)}`);
      throw new ExError('TMT_OPEN_API_PARAM_ERROR', '入参错误请检查');
    }
    if (ids.length > 1500) {
      ctx.logger.error(`${this.logPrefix} saleOrderUpdateBatch saleOrder TMT_OPEN_API_PARAM_ERROR:批量取消出售数量超过1500`);
      throw new ExError('TMT_OPEN_API_PARAM_ERROR', '批量取消数量过大，请勿添加过多商品');
    }
    const {failedUpdates,successfulUpdates} = await ctx.service.item.saleOrder.saleOrderUpdateBatch(saleOrders);
    ctx.body = {
      code: 0,
      desc: '',
      data: {
        successfulUpdates,
        failedUpdates,
      },
    };

  }

  public async sale_order_list() {
    const { ctx } = this;
    if (typeof (this.validator.sale_order_list) === 'function') {
      await this.validator.sale_order_list();
    }
    const allowSearchFields: SearchField[] = [
      {
        key: '_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'examine_status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'item_name',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'user.type',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'item_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
    ];

    const allowSortFields = [
      '_id',
    ];

    // const queryOfLast = [];
    // if(ctx.query.type) {
    //   queryOfLast.push()
    // };

    const queryParse = urlParse(ctx.query, {
      search: {
        allowSearchFields,
      },
      sort: {
        allowSortFields,
      },
    });

    const query: SaleOrderQuery = filingQuerysByMongo({ examine_status: ItemExamineStatusEnum.allow }, queryParse);
    const sorts = convertSorts(filingSortsByMongo({ _id: -1 }, queryParse));
    const projects = {
      sale_user_id: 1,
      chain_item_id: 1,
      buy_user_id: 1,
      examine_status: 1,
      order_status: 1,
      sale_from: 1,
      order_tiem: 1,
      order_amount: 1,
      created_at: 1,
      updated_at: 1,
      chain_address: '$item.chain_address',
      item_info: {
        item_id: '$item.item_info.item_id',
        item_name: '$item.item_info.item_name',
        item_icon_url: '$item.item_info.item_icon_url',
      },
      type: '$user.type',
    };
    const aggregate = [
      {
        $lookup: {
          from: 'item',
          localField: 'chain_item_id',
          foreignField: 'chain_item_id',
          as: 'item',
        },
      },
      {
        $unwind: '$item',
      },
      {
        $match: query,
      },
      {
        $lookup: {
          from: 'users',
          localField: 'chain_wallet_id',
          foreignField: 'open_info.chain_address',
          as: 'user',
        },
      },
      {
        $unwind: '$user',
      },
    ];
    ctx.logger.info(`${this.logPrefix} sale_order_create`, JSON.stringify(aggregate), sorts);

    let saleOrder: SaleOrderEntity[] = [];
    const options = {
      page: ctx.state.queryOptions.page,
      limit: ctx.state.queryOptions.limit,
      sort: sorts,
      project: projects,
    };
    saleOrder = await ctx.service.item.saleOrder.aggregateSaleOrder(aggregate, options);

    ctx.body = {
      code: 0,
      desc: '',
      data: saleOrder,
    };

  }

  public async user_item_sale_order() {
    const { ctx } = this;
    if (typeof this.validator.user_item_sale_order === 'function') {
      await this.validator.user_item_sale_order();
    }
    const allowSearchFields: SearchField[] = [
      {
        key: '_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'examine_status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'item_name',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'user.type',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'item_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
    ];

    const allowSortFields = [
      'created_at',
      'order_amount',
    ];

    // const queryOfLast = [];
    // if(ctx.query.type) {
    //   queryOfLast.push()
    // };

    const queryParse = urlParse(ctx.query, {
      search: {
        allowSearchFields,
      },
      sort: {
        allowSortFields,
      },
    });

    const query: SaleOrderQuery = filingQuerysByMongo({ examine_status: ItemExamineStatusEnum.allow }, queryParse);
    if (!query.item_id) throw new ExError('TMT_DATA_NOT_EXIST', 'the item_id is not exist');
    query.order_status = {
      $eq: ItemSaleStatusEnum.sell,
    };

    const sorts = convertSorts(filingSortsByMongo({}, queryParse));
    const userId = await ctx.service.user.user.getLoginUserId();
    sorts._id = -1;
    const projects = {
      sale_user_id: 1,
      item_id: 1,
      buy_user_id: 1,
      examine_status: 1,
      order_status: 1,
      sale_from: 1,
      order_type: 1,
      order_tiem: 1,
      order_amount: 1,
      likes_count: 1,
      created_at: 1,
      'user.patbg_detail.nickname': 1,
      'user.patbg_detail.avatar': 1,
      'user.patbg_detail.is_merchant': 1,
      item_name: '$steam_items.item_name',
      icon_url: '$steam_items.icon_url',
      model_3d_detail: '$steam_items.model_3d_detail',
      is_sell: '$steam_items.extends.is_presell',
      sell_time: '$steam_items.extends.sell_time',
    };
    const aggregate = [
      {
        $match: query,
      },
      {
        $lookup: {
          from: 'users',
          localField: 'sale_user_id',
          foreignField: '_id',
          as: 'user',
        },
      },
      {
        $unwind: '$user',
      },
      {
        $lookup: {
          from: 'steam_items',
          localField: 'item_id',
          foreignField: '_id',
          as: 'steam_items',
        },
      },
      {
        $unwind: '$steam_items',
      },
    ];
    ctx.logger.debug(`${this.logPrefix} sale_order_create`, JSON.stringify(aggregate), sorts);

    let lastOrderAmount = 0;
    let saleOrder: SaleOrderEntity[] = [];
    let itemId;
    const saleCount = await ctx.service.item.saleOrder.countAggregateItem(aggregate);

    if (saleCount > 0) {
      const options = {
        page: ctx.state.queryOptions.page,
        limit: ctx.state.queryOptions.limit,
        sort: sorts,
        project: projects,
      };
      saleOrder = await ctx.service.item.saleOrder.aggregateSaleOrder(aggregate, options);

      for (const order of saleOrder) {
        if (_.get(order, 'steam_items.model_3d_detail') && !_.get(order, 'steam_items.model_3d_detail.is_add_3d_model')) {
          delete order.steam_items.model_3d_detail.background_gif;
          delete order.steam_items.model_3d_detail.model_3d_gif;
          delete order.steam_items.model_3d_detail.model_3d;
        }
        if (order.user && order.user.patbg_detail) {
          order.user.patbg_detail.nickname = await ctx.service.item.saleOrder.maskNickname(order.user.patbg_detail.nickname);
        }
      }
      itemId = _.get(saleOrder, '[0].item_id');

      if (itemId) {
        // 最新成交价
        const lastSaleOrder = await ctx.service.item.saleOrder.queryJustOne(
          { item_id: itemId, order_status: ItemSaleStatusEnum.sellOut },
          [ 'order_amount' ],
          { sort: { order_tiem: -1 } },
        );
        if (lastSaleOrder) {
          lastOrderAmount = lastSaleOrder.order_amount;
        } else {
          const issueItem = await ctx.service.item.issueItem.queryIssueItem({ item_id: itemId }, [ 'price' ]);
          lastOrderAmount = issueItem?.price;
        }
      }
    } else {
      const pre_item_id = query.item_id;
      const objectIdString = pre_item_id['$eq'];
      itemId = Types.ObjectId(objectIdString);

      // 最新成交价
      const lastSaleOrder = await ctx.service.item.saleOrder.queryJustOne(
        { item_id: itemId, order_status: ItemSaleStatusEnum.sellOut },
        [ 'order_amount' ],
        { sort: { order_tiem: -1 } },
      );
      if (lastSaleOrder) {
        lastOrderAmount = lastSaleOrder.order_amount;
      } else {
        const issueItem = await ctx.service.item.issueItem.queryIssueItem({ item_id: itemId }, [ 'price' ]);
        lastOrderAmount = issueItem?.price;
      }
    }
    //获取时间
    let date =  moment();
    let mDate = ctx.service.statistics.setMomentUtcOffset(date);
    let todayStart = mDate.clone().startOf('day').toDate();
    let todayEnd = mDate.toDate();
    ctx.logger.debug(`${this.logPrefix} 开始结束时间：`, JSON.stringify({todayStart,todayEnd}));
    //获取今日最高最低成交价：
    const [maxOrder, minOrder] = await Promise.all([
      ctx.service.item.saleOrder.queryJustOne(
        {item_id: itemId, order_status: ItemSaleStatusEnum.sellOut,
          order_tiem: {
            $gte: todayStart,
            $lte: todayEnd,
          },
        },
        [ 'order_amount' ],
        {sort: { order_amount: -1 },
        }),
      ctx.service.item.saleOrder.queryJustOne(
        {item_id: itemId, order_status: ItemSaleStatusEnum.sellOut,
          order_tiem: {
            $gte: todayStart,
            $lte: todayEnd,
          },
        }, [ 'order_amount' ],
        {
          sort: { order_amount: 1 },
        }),
    ]);
    ctx.logger.info(`${this.logPrefix} 最大最小订单成交价 maxOrder minOrder ：`, JSON.stringify({maxOrder,minOrder}));


    const steamItem = await ctx.service.item.steamItem.queryJustOne({ _id: query.item_id });
    const issueItem = await ctx.service.item.issueItem.queryIssueItem({ item_id: steamItem._id }, [ '_id' ,'issuer_name','issuer_short_name','quantity','price','sales_volume','sale_end']);
    //发行数量
    const quantity = issueItem?.quantity;
    //发行价格
    const price = issueItem?.price;
    //获取商品id的持仓数
    const userItemCounts = await ctx.service.ycOpm.countValidUserItemByItemIds([ String(steamItem._id) ]);
    ctx.logger.info(`${this.logPrefix} 持仓数 userItemCounts：`, JSON.stringify({userItemCounts}));
    let holderQuantity = _.get(_.first(userItemCounts), 'count', 0);
    // 获取上一个交易日的收盘价
    const lastDailyQuotation = await ctx.service.item.dailyQuotation.getLastDailyQuotation(steamItem._id);
    const quotation = lastDailyQuotation || price;
    //市值：(该商品首发剩余库存（首发已结束不计算）+该商品当前总用户持仓数量) * 收盘价
    const sale_end = issueItem.sale_end ? moment(issueItem.sale_end) : null;
    if (!sale_end || sale_end.isAfter(moment())) {
      holderQuantity = holderQuantity + issueItem.quantity - issueItem.sales_volume;
    }
    const marketAmount = holderQuantity*quotation;
    const highestPrice = maxOrder ? maxOrder.order_amount : quotation;
    const lowestPrice = minOrder ? minOrder.order_amount : quotation;
    //获取商品最高最低限价
    const referencePricesArrr = await ctx.service.item.saleOrder.referencePrice([new ObjectId(steamItem._id)]);
    const  maxPrice = _.get(_.first(referencePricesArrr),'maxPrice');
    const  minPrice = _.get(_.first(referencePricesArrr),'minPrice');
    //当日成交量&成交额
    const pipeline = [
      {
        $unwind: "$extends.items"
      },
      {
        $match: {
          "extends.items.item_id": new ObjectId(steamItem._id),
          "status": ItemWithdrawOrderStatusEnum.Done,
          "created_at": {
            $gte: new Date(todayStart.toISOString()), // 开始时间
            $lte: new Date(todayEnd.toISOString())    // 结束时间
          }
        }
      },
      {
        $group: {
          _id: "$extends.items.item_id",
          totalAmount: { $sum: "$pay_amount" }, // 计算 pay_amount 总和
          orderCount: { $sum: 1 } // 统计订单数量
        }
      }
    ];
    const withdrawOrders = await ctx.service.item.itemWithdrawOrder.model.aggregate(pipeline);
    ctx.logger.info(`${this.logPrefix} 二手成交 withdrawOrders：`, JSON.stringify(withdrawOrders));
    ctx.logger.info(`${this.logPrefix} 二手成交 pipeline：`, JSON.stringify(pipeline,null,2));
    let totalDrawAmount = 0;
    let orderDrawCount = 0;
    if(withdrawOrders){
      totalDrawAmount = _.get(_.first(withdrawOrders),'totalAmount',0);
      orderDrawCount = _.get(_.first(withdrawOrders),'orderCount',0);
    }

    const match = await ctx.service.match.queryMatch({ fid: query.item_id });
    const ipIds = _.map(match.ip_info, '_id');
    const ipQuery = {
      _id: {
        $in: ipIds,
      },
      level: 2,
    };

    const ipInfos = await ctx.service.item.ipClassify.queryIpClassifys(ipQuery, ['_id', 'name', 'main_icon']);
    const redisPrefix = _.get(ctx.app.config, 'custom.redis.prefix', 'TMTBG');
    if (userId && ipInfos.length > 0) {
      for (const ipInfo of ipInfos) {
        const key = `${redisPrefix}:IpSeen:${String(userId)}`;
        const userIpIds = await ctx.app.redis.get(key);
        const ipId = String(ipInfo._id);
        if (userIpIds) {
          let ids = _.split(userIpIds, ':::');
          if (ipId !== ids[0]) {
            ids = [
              ipId,
              ids[0]
            ]
            ctx.app.redis.setex(key, 604800, _.join(ids, ':::'));
          }
        } else {
          ctx.app.redis.setex(key, 604800, ipId);
        }
      }
    }

    const item_info = {
      ip_info: ipInfos,
      item_name: match.name,
      likes_total: match.likes_total,
      match_id: match._id,
      icon_url: steamItem.icon_url,
      replenish: _.get(steamItem, 'market_prices.unx.sell_listings', 0) <= 0,
      is_sell: false,
      sell_time: moment().format('yyyy-MM-DD'),
      issue_item_id: issueItem?._id,
    };
    const res = {
      list: saleOrder,
      count: saleCount,
      lastOrderAmount,
      item_info,
      quantity:quantity,
      holderQuantity:holderQuantity,
      quotation:quotation,
      marketAmount:marketAmount,
      maxPrice:maxPrice,
      minPrice:minPrice,
      highestPrice:highestPrice,
      lowestPrice:lowestPrice,
      totalDrawAmount:totalDrawAmount,
      orderDrawCount:orderDrawCount
    };
    ctx.body = {
      code: 0,
      desc: '',
      data: res,
    };

  }

  public async user_item_sale_order_list() {
    const { ctx } = this;
    if (typeof this.validator.user_item_sale_order === 'function') {
      await this.validator.user_item_sale_order();
    }
    const allowSearchFields: SearchField[] = [
      {
        key: '_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'examine_status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'item_name',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'item_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
    ];

    const allowSortFields = [
      'created_at',
      'order_amount',
    ];

    // const queryOfLast = [];
    // if(ctx.query.type) {
    //   queryOfLast.push()
    // };

    const queryParse = urlParse(ctx.query, {
      search: {
        allowSearchFields,
      },
      sort: {
        allowSortFields,
      },
    });

    const query: SaleOrderQuery = filingQuerysByMongo({ examine_status: ItemExamineStatusEnum.allow }, queryParse);
    if (!query.item_id) throw new ExError('TMT_DATA_NOT_EXIST', 'the item_id is not exist');
    query.order_status = {
      $eq: ItemSaleStatusEnum.sell,
    };

    const sorts = convertSorts(filingSortsByMongo({}, queryParse));
    sorts._id = -1;
    const projects = {
      sale_user_id: 1,
      item_id: 1,
      examine_status: 1,
      order_status: 1,
      sale_from: 1,
      order_type: 1,
      order_tiem: 1,
      order_amount: 1,
      likes_count: 1,
      created_at: 1,
      'user.patbg_detail.nickname': 1,
      'user.patbg_detail.avatar': 1,
    };
    const aggregate = [
      {
        $match: query,
      },
      {
        $lookup: {
          from: 'users',
          localField: 'sale_user_id',
          foreignField: '_id',
          as: 'user',
        },
      },
      {
        $unwind: '$user',
      },
      {
        $lookup: {
          from: 'steam_items',
          localField: 'item_id',
          foreignField: '_id',
          as: 'steam_items',
        },
      },
      {
        $unwind: '$steam_items',
      },
    ];
    ctx.logger.info(`${this.logPrefix} sale_order_create`, JSON.stringify(aggregate), sorts);

    let saleOrder: SaleOrderEntity[] = [];
    // if (saleCount > 0) {
    const options = {
      page: ctx.state.queryOptions.page,
      limit: ctx.state.queryOptions.limit,
      sort: sorts,
      project: projects,
    };
    saleOrder = await ctx.service.item.saleOrder.aggregateSaleOrder(aggregate, options);
    // }

    const steamItem = await ctx.service.item.steamItem.queryJustOne({ _id: query.item_id });
    const match = await ctx.service.match.queryMatch({ fid: query.item_id });
    const ipIds = _.map(match.ip_info, '_id');
    const ipQuery = {
      _id: {
        $in: ipIds,
      },
      level: 2,
    };
    const ipInfos = await ctx.service.item.ipClassify.queryIpClassifys(ipQuery, ['name', 'main_icon']);
    const item_info = {
      ip_info: ipInfos,
      item_name: match.name,
      likes_total: match.likes_total,
      match_id: match._id,
      is_like: false,
      icon_url: steamItem.icon_url,
    };
    const userId = await ctx.service.user.user.getLoginUserId();
    if (userId) {
      const isLike = await ctx.service.user.userItemLikes.isLike(userId, match.fid, match._id);
      item_info.is_like = isLike;
    }

    const res = {
      list: saleOrder,
    };
    ctx.body = {
      code: 0,
      desc: '',
      data: res,
    };

  }

  /**
   * 指定商品出售列表
   */
  public async getSaleOrderListByItemId() {
    const { ctx } = this;
    if (typeof this.validator.getSaleOrderListByItemId === 'function') {
      await this.validator.getSaleOrderListByItemId();
    }

    const itemId = ctx.state.item_id;
    const aggregate = [
      {
        $match: {
          item_id: itemId,
          order_status: {
            $eq: ItemSaleStatusEnum.sell,
          },
        },
      },
      {
        $sort: {
          order_amount: 1,
          _id: -1,
        },
      },
      {
        $project: {
          _id: 1,
          order_amount: 1,
          item_id: 1,
          sale_user_id: 1,
          order_status: 1,
          created_at: 1,
        },
      },
    ];
    const saleOrders = await ctx.service.item.saleOrder.aggregateSaleOrder(aggregate, {
      page: ctx.state.queryOptions.page,
      limit: ctx.state.queryOptions.limit,
    });
    // 获取出售用户信息
    const userIds = _.map(saleOrders, 'sale_user_id');
    const userInfoMap = await ctx.service.user.user.getUserSimpleInfoMapByUserIds(userIds);
    // 获取发行商品信息
    const issueItem = await ctx.service.item.issueItem.queryIssueItem({ item_id: itemId }, [ 'item_name' ]);

    for (const saleOrder of saleOrders) {
      const userInfo = userInfoMap.get(String(saleOrder.sale_user_id));
      if (userInfo) {
        userInfo.nickname = maskNickname(userInfo.nickname);
      }
      saleOrder.user_info = userInfo;
    }

    const rspData = {
      list: saleOrders,
      issue_item: {
        _id: issueItem._id,
        item_name: issueItem.item_name,
      },
      has_more: saleOrders.length === ctx.state.queryOptions.limit,
    };
    ctx.body = {
      code: 0,
      desc: '',
      data: rspData,
    };
  }

  /**
   * 指定发行商品的二手交易概要信息
   */
  public async getIssueItemSaleOrderSummary() {
    const { ctx } = this;
    if (typeof this.validator.getIssueItemSaleOrderSummary === 'function') {
      await this.validator.getIssueItemSaleOrderSummary();
    }
    const itemId = ctx.state.item_id;
    const issueItem = await ctx.service.item.issueItem.queryIssueItem({ item_id: itemId },
      [ '_id', 'item_id', 'item_name', 'image_url', 'issuer_short_name', 'quantity', 'price', 'sales_volume', 'sale_end', 'price_control', 'price_limit', 'price_range' ]);
    const userItemCountMap = await ctx.service.ycOpm.loadUserItemCountsFromCacheOrRemote([ itemId ]);
    const userItemCount: any = userItemCountMap.get(String(itemId)) ?? { count: 0 };
    // 流通数量 = 该商品首发剩余库存(首发已结束不计算)+该商品当前总用户持仓数量(排除已提货、已融合的，持仓数量每小时计算更新一次)
    let totalCirculation = userItemCount.count ?? 0; // 获取流通数量，默认为 0
    const sale_end = issueItem.sale_end ? moment(issueItem.sale_end) : null;
    if (!sale_end || sale_end.isAfter(moment())) {
      totalCirculation = totalCirculation + issueItem.quantity - issueItem.sales_volume;
    }
    // 获取上个交易日收盘价
    const dateStr = ctx.service.statistics.getDateStr(moment(), 'YYYY-MM-DD');
    const preDailyQuotationMap = await ctx.service.item.dailyQuotation.getPreDailyQuotationsWithCache([ itemId ], dateStr);
    // 获取上个交易日收盘价
    const preDailyQuotation: any = preDailyQuotationMap.get(String(itemId));
    const preClosePrice = _.get(preDailyQuotation, 'close_price', issueItem.price);

    const startOfDay = moment().utc().utcOffset(8).startOf('day').utc().toDate();
    const endOfDay = moment().utc().utcOffset(8).endOf('day').utc().toDate();
    const aggregate = [
      {
        $match: {
          created_at: {
            $gte: startOfDay,
            $lte: endOfDay,
          },
          status: 1,
          'extends.items': { $elemMatch: { item_id: itemId } },
        },
      },
      {
        $sort: { created_at: -1 },
      },
      {
        $unwind: '$extends.items',
      },
      {
        $match: {
          'extends.items.item_id': itemId,
        },
      },
      {
        $group: {
          _id: '$extends.items.item_id',
          // 成交量
          trade_volume: { $sum: 1 },
          // 最高成交价
          max_trade_price: { $max: '$extends.items.sell_price' },
          // 最低成交价
          min_trade_price: { $min: '$extends.items.sell_price' },
          // 最新成交价
          last_trade_price: { $first: '$extends.items.sell_price' },
          // 成交额
          trade_amount: { $sum: '$extends.items.sell_price' },
        },
      },
    ];
    ctx.logger.info(`getIssueItemSaleOrderSummary item_id(${itemId})，aggregate：${JSON.stringify(aggregate)}`);
    const itemOrderDatas = await ctx.service.item.itemWithdrawOrder.aggregateWithdrawOrder(aggregate);
    const itemOrderData = _.first(itemOrderDatas) ?? {};
    // 当天最新成交价
    const todayLastTradePrice = itemOrderData?.last_trade_price;
    let lastTradePrice;
    ctx.logger.info(`二手详情 item_id(${itemId})，上个交易日收盘价：${preClosePrice} preDailyQuotationMap: ${JSON.stringify(preDailyQuotationMap)}`);
    if (todayLastTradePrice) {
      lastTradePrice = todayLastTradePrice;
    } else {
      const lastSaleOrderMap = await ctx.service.item.saleOrder.getLastSaleOrderMapByItemIds([ itemId ]);
      lastTradePrice = lastSaleOrderMap.get(String(itemId)) ?? issueItem.price;
    }
    // 涨跌幅
    let priceChangeRate = 0;
    // 最近两条收盘价记录
    const latestClosePriceRecords = await ctx.service.item.dailyQuotation.queryDailyQuotations(
      {
        item_id: itemId,
      }, [ 'close_price' ],
      {
        sort: { date: -1 },
        limit: 2,
      });
    let latestClosePrice = 0; // 最新收盘价
    let secondClosePrice = 0; // 次新收盘价
    if (latestClosePriceRecords.length > 0) {
      latestClosePrice = latestClosePriceRecords[0].close_price;
      if (latestClosePriceRecords.length > 1) {
        secondClosePrice = latestClosePriceRecords[1].close_price;
      } else {
        // 只有一个收盘价时，次新收盘价用首发价格兜底
        secondClosePrice = issueItem.price;
      }
    }
    // 涨跌幅默认计算公式：（最新收盘价 - 次新收盘价）/ 次新收盘价
    if (latestClosePrice > 0 && secondClosePrice > 0) {
      priceChangeRate = _.round(((latestClosePrice - secondClosePrice) / secondClosePrice) * 100, 2);
    }
    // 如果当天有交易 且 当前时间在交易时间段内
    // 涨跌幅计算公式：（当天最新成交价 - 上个交易日收盘价）/ 上个交易日收盘价
    if (todayLastTradePrice) {
      const allowSale = ctx.service.item.saleOrder.allowSale();
      if (allowSale) {
        priceChangeRate = _.round(((todayLastTradePrice - preClosePrice) / preClosePrice) * 100, 2);
      }
    }
    // 计算市值 市值公式=(该商品首发剩余库存（首发已结束不计算）+该商品当前总用户持仓数量) * 最新成交价
    const marketAmount = totalCirculation * lastTradePrice;
    // 计算限价
    let priceLimitConf = _.get(ctx.app.config.custom, 'price_limit', {});
    // 涨跌幅限额
    let priceRangeConf = _.get(ctx.app.config.custom, 'price_range', {});
    if (issueItem?.price_control) {
      priceLimitConf = _.get(issueItem, 'price_limit', {});
      priceRangeConf = _.get(issueItem, 'price_range', {});
    }
    const { min_ratio: minRatio = 0, max_ratio: maxRatio = 0 } = priceLimitConf;
    let maxPrice = _.ceil(Number(preClosePrice * (maxRatio / 100 + 1)).toFixed(2));
    let minPrice = _.ceil(Number(preClosePrice * (1 - minRatio / 100)).toFixed(2));
    const { min_ratio: rangeMinRatio = 0, max_ratio: rangeMaxRatio = 0 } = priceRangeConf;
    const rangeMaxPrice = _.ceil(Number(issueItem?.price * (rangeMaxRatio / 100 + 1)).toFixed(2));
    const rangeMinPrice = _.ceil(Number(issueItem?.price * (1 - rangeMinRatio / 100)).toFixed(2));
    maxPrice = maxPrice > rangeMaxPrice ? rangeMaxPrice : maxPrice;
    minPrice = minPrice < rangeMinPrice ? rangeMinPrice : minPrice;
    const simpleIssueItem = _.pick(issueItem, [ '_id', 'item_id', 'item_name', 'image_url', 'issuer_short_name', 'quantity' ]);
    const summaryData = {
      ... simpleIssueItem,
      // 流通数量
      total_circulation: totalCirculation,
      // 市值
      market_amount: marketAmount,
      // 最新成交价
      last_trade_price: lastTradePrice,
      // 涨跌幅
      price_change_rate: priceChangeRate,
      // 最高成交价
      max_trade_price: _.get(itemOrderData, 'max_trade_price', preClosePrice),
      // 最低成交价
      min_trade_price: _.get(itemOrderData, 'min_trade_price', preClosePrice),
      // 成交额
      trade_amount: _.get(itemOrderData, 'trade_amount', 0),
      // 成交量
      trade_volume: _.get(itemOrderData, 'trade_volume', 0),
      // 最高限价
      max_price: Math.max(maxPrice, 0),
      // 最低限价
      min_price: Math.max(minPrice, 0),
    };
    ctx.body = {
      code: 0,
      desc: '',
      data: summaryData,
    };
  }

  public async saleOrderDetail() {
    const { ctx } = this;

    if (typeof this.validator.user_item_sale_order_detail === 'function') {
      await this.validator.user_item_sale_order_detail();
    }
    const saleOrder = ctx.state.saleOrder;
    const itemId = saleOrder.item_id;
    const issueItem = await ctx.service.item.issueItem.queryIssueItem({ item_id: itemId });
    if (!issueItem) {
      throw new ExError('TMT_DATA_NOT_EXIST');
    }
    let data = _.pick(issueItem, [
      'item_name', 'ip_classify_names', 'item_specs', 'issuer_name', 'issuer_short_name', 'copyright_name', 'copyright_no', 'seller_name', 'seller_short_name', 'quantity',
      'price', 'issue_time', 'sale_mode', 'delivery_time', 'image_url', 'image_infos', 'detail', 'status', 'circulation_status', 'sales_volume', 'chain_hash',
    ]);
    data.register_agency = '安徽省文化产权交易所';
    let isPreSale = false;
    if (issueItem.sale_mode === SaleModeEnum.Presale) {
      data.is_delivery = (issueItem?.delivery_time ? issueItem?.delivery_time : new Date()) < new Date();
      isPreSale = (issueItem?.delivery_time ? issueItem?.delivery_time : new Date()) > new Date();
    }
    data.is_pre_sale = isPreSale;
    const saleOrderPick = _.pick(saleOrder, [ '_id', 'sale_user_id', 'item_id', 'order_status', 'order_type', 'order_amount', 'user_item_id' ]);
    data = { ...data, ...saleOrderPick };
    // 查询出售数量
    const saleCount = await ctx.service.item.saleOrder.count({
      item_id: itemId,
      order_status: { $gt: ItemSaleStatusEnum.close },
    });
    data.sale_count = saleCount ?? 0;
    // 查询卖家信息
    const userMap = await ctx.service.user.user.getUserSimpleInfoMapByUserIds([ saleOrder.sale_user_id ]);
    data.sale_user_avatar = userMap.get(String(saleOrder.sale_user_id))?.avatar ?? '';
    data.sale_user_nickname = userMap.get(String(saleOrder.sale_user_id))?.nickname ?? '';
    ctx.body = {
      code: 0,
      desc: '',
      data,
    };

  }

  public async user_item_sale_order_detail() {
    const { ctx } = this;

    if (typeof this.validator.user_item_sale_order_detail === 'function') {
      await this.validator.user_item_sale_order_detail();
    }

    const saleOrder = ctx.state.saleOrder;
    const project = {
      _id: 1,
      sale_user_id: 1,
      item_id: 1,
      order_status: 1,
      order_type: 1,
      order_amount: 1,
      likes_count: 1,
      user_item_id: 1,
      bargain: 1,
      ip_info: '$steam_items.ip_info',
      image_infos: '$steam_items.image_infos',
      trademark_info: '$steam_items.trademark_info',
      item_classify_info: '$steam_items.item_classify_info',
      model_3d_detail: '$steam_items.model_3d_detail',
      detail: '$steam_items.detail',
      detail_h5: '$steam_items.detail_h5',
      icon_url: '$steam_items.icon_url',
      item_name: '$steam_items.item_name',
      nickname: '$user.patbg_detail.nickname',
      avatar: '$user.patbg_detail.avatar',
      is_merchant: '$user.patbg_detail.is_merchant',
      item_source: '$steam_items.item_source',
      is_sell: '$steam_items.extends.is_presell',
      sell_time: '$steam_items.extends.sell_time',
      sell_listings: '$steam_items.market_prices.unx.sell_listings',
    };
    const aggregate = [
      {
        $match: { _id: saleOrder._id },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'sale_user_id',
          foreignField: '_id',
          as: 'user',
        },
      },
      {
        $unwind: '$user',
      },
      {
        $lookup: {
          from: 'steam_items',
          localField: 'item_id',
          foreignField: '_id',
          as: 'steam_items',
        },
      },
      {
        $unwind: '$steam_items',
      },
      {
        $project: project,
      },
    ];

    const aggregateData = await ctx.service.item.saleOrder.aggregateSaleOrder(aggregate);
    const res = aggregateData.pop();

    res.is_like = false;
    const userId = await ctx.service.user.user.getLoginUserId();
    if (userId) {
      const favorite = await ctx.service.user.favorite.queryFavorite({ user_id: userId, relate_id: res._id, status: StatusEnum.Enable });
      if (favorite) {
        res.is_like = true;
      }
    }
    const ipIds = _.map(res.ip_info, '_id');
    const ipQuery = {
      _id: {
        $in: ipIds,
      },
      level: 2,
    };
    const ipInfos = await ctx.service.item.ipClassify.queryIpClassifys(ipQuery, ['_id', 'name', 'main_icon']);
    const redisPrefix = _.get(ctx.app.config, 'custom.redis.prefix', 'TMTBG');
    if (userId && ipInfos.length > 0) {
      for (const ipInfo of ipInfos) {
        const key = `${redisPrefix}:IpSeen:${String(userId)}`;
        const userIpIds = await ctx.app.redis.get(key);
        const ipId = String(ipInfo._id);
        if (userIpIds) {
          let ids = _.split(userIpIds, ':::');
          if (ipId !== ids[0]) {
            ids = [
              ipId,
              ids[0]
            ]
            ctx.app.redis.setex(key, 604800, _.join(ids, ':::'));
          }
        } else {
          ctx.app.redis.setex(key, 604800, ipId);
        }
      }
    }
    res.ip_info = ipInfos;
    // 过滤品牌
    _.remove(res.trademark_info, trademark => trademark.level !== 2);
    // 增加气仓状态
    if (res.user_item_id) {
      const userItem = await ctx.service.item.userItem.queryUserItem({ _id: res.user_item_id }, ['status']);
      const userItemStatus = _.get(userItem, 'status');
      if (userItemStatus) {
        _.set(res, 'user_item_status', userItemStatus);
      }
    }

    res.replenish = _.get(res, 'sell_listings', 0) <= 0;
    ctx.logger.info('res.replenish:' + res.replenish);
    // 判断预售商品 和  预售时间
    const nowTime = new Date().getTime();
    const isExist = _.get(res, 'sell_time', 0);
    if (isExist && isExist !== 0) {
      const sellTime = new Date(isExist).getTime();
      ctx.logger.info('nowTime:' + nowTime + '-----' + 'sellTime:' + sellTime);
      if (nowTime < sellTime) {
        res.is_sell = true;
        res.sell_time = moment(sellTime + 28800).format('yyyy-MM-DD');
      }
    }
    if (!res.is_sell) {
      res.is_sell = false;
    }

    if (!res.image_infos) {
      res.image_infos = [];
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: res,
    };
  }

  /**
   * 出售列表
   */
  public async sale_list() {
    const { ctx } = this;
    if (typeof (this.validator.sale_order_list) === 'function') {
      await this.validator.sale_order_list();
    }
    const allowSearchFields: SearchField[] = [
      // {
      //   key: 'keyword',
      //   type: SearchTypeEnum.STRING,
      //   allow_ops: [
      //     UrlParseSearchOp.EQ,
      //   ],
      // },
      {
        key: 'extends.order_name',
        type: SearchTypeEnum.STRING,
        allow_ops: [
          UrlParseSearchOp.LIKE,
        ],
      },
      {
        key: 'extends.open_box',
        type: SearchTypeEnum.NUMBER,
        allow_ops: [
          UrlParseSearchOp.EQ,
        ],
      },
    ];

    const allowSortFields = [
      'order_amount', 'created_at', 'likes_count', 'item.index',
    ];

    // const queryOfLast = [];
    // if(ctx.query.type) {
    //   queryOfLast.push()
    // };

    const queryParse = urlParse(ctx.query, {
      search: {
        allowSearchFields,
      },
      sort: {
        allowSortFields,
      },
    });

    const query: SaleOrderQuery = filingQuerysByMongo({}, queryParse);
    const sorts = convertSorts(filingSortsByMongo({}, queryParse));

    const user = ctx.state.user;
    let firstQuery;
    if (user) {
      firstQuery = {
        $or: [
          {
            order_status: { $in: [2, 4] },
            examine_status: ItemExamineStatusEnum.allow,
          },
          {
            examine_status: ItemExamineStatusEnum.waiting,
            sale_user_id: user._id,
          },
        ],
      };
    } else {
      firstQuery = {
        order_status: { $in: [2, 4] },
        examine_status: ItemExamineStatusEnum.allow,
      };
    }

    // 通过关键词搜索
    // const keyword = _.get(query, 'keyword.$eq');
    // const steamItemsIds: ObjectID[] = [];
    // const nftBoxQuery = {};
    const flag = true;
    let saleOrders: SaleOrderEntity[] = [];
    // if (keyword) {
    //   const steamItemQuery: any = {};
    //   delete query.keyword;
    //   if (checkIsObjectId(keyword)) {
    //     const objId = new ObjectID(keyword);
    //     steamItemQuery._id = objId;
    //     // query.$or = [
    //     //   { _id: objId },
    //     //   { 'item._id': objId},
    //     // ];
    //   } else {
    //     steamItemQuery.item_name = { $regex: keyword, "$options": "i" };
    //     // query.$or = [
    //     //   { 'steam_item.item_name': { $regex: keyword, "$options" : "i" } },
    //     // ];
    //   }
    //   steamItemQuery.appid = AppidEnum.Nft;
    //   const steamItems = await ctx.service.item.steamItem.queryDatas(steamItemQuery);
    //   for (const steamItem of steamItems) {
    //     steamItemsIds.push(steamItem._id);
    //   }
    //   if (steamItemsIds.length > 0) {
    //     nftBoxQuery['item.open_box'] = {
    //       $ne: 1
    //     }
    //     firstQuery.item_id = {
    //       $in: steamItemsIds,
    //     }
    //   } else {
    //     flag = false;
    //   }
    // }
    if (flag) {
      const projects = {
        nft_item_id: 1,
        item_id: '$item._id',
        order_amount: 1,
        order_status: 1,
        open_box: '$item.open_box',
        nft_box_id: '$item.nft_box_id',
        item_name: '$steam_item.item_name',
        icon_url: '$steam_item.icon_url',
        nickname: '$user.patbg_detail.nickname',
        avatar: '$user.patbg_detail.avatar',
        likes_count: '$item.likes_count',
        model_3d_gif: '$steam_item.model_3d_detail.model_3d_gif',
        index: '$item.index',
        original_stock: '$steam_item.market_prices.unx.original_stock',
      };
      const aggregate = [
        {
          $match: query,
        },
        {
          $match: firstQuery,
        },
        { $sort: sorts },
        {
          $lookup: {
            from: 'item',
            localField: 'nft_item_id',
            foreignField: '_id',
            as: 'item',
          },
        },
        {
          $unwind: '$item',
        },
        { $skip: ctx.state.queryOptions.offset },
        { $limit: ctx.state.queryOptions.limit },
        {
          $lookup: {
            from: 'steam_items',
            localField: 'item.item_id',
            foreignField: '_id',
            as: 'steam_item',
          },
        },
        {
          $unwind: '$steam_item',
        },
        {
          $match: {
            'steam_item.tmt_show': { $ne: false },
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'item.open_user_id',
            foreignField: 'open_info.open_user_id',
            as: 'user',
          },
        },
        {
          $unwind: '$user',
        },
      ];
      ctx.logger.info(`${this.logPrefix} nft_sale_list`, JSON.stringify(aggregate), sorts);

      const options = {
        // page: ctx.state.queryOptions.page,
        // limit: ctx.state.queryOptions.limit,
        // sort: sorts,
        project: projects,
      };
      saleOrders = await ctx.service.item.saleOrder.aggregateSaleOrder(aggregate, options);
      // saleOrders = await ctx.service.item.saleOrder.query(firstQuery,{}, {sort:sorts});
      await ctx.service.nft.nftBox.get_nft_box(saleOrders, false);
      const userId = ctx.service.user.user.getLoginUserId();
      if (userId) {
        const length = saleOrders.length;
        if (length > 0) {
          for (let i = 0; i < length; i++) {
            const order = saleOrders[i];
            const userItemLikeQuery: UserItemLikesQuery = {
              item_id: { $eq: String(order.nft_item_id) },
              user_id: { $eq: userId },
            };

            const like = await ctx.service.user.userItemLikes.queryUserItemLikes(userItemLikeQuery, { _id: 1 });
            if (like) {
              saleOrders[i].like_status = true;
            }
          }
        }
      }
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: saleOrders,
    };

  }

  /**
   * 用户自查出售列表
   */
  public async user_sale_order_list() {
    const { ctx } = this;
    if (typeof (this.validator.user_sale_order_list) === 'function') {
      await this.validator.user_sale_order_list();
    }

    const user = ctx.state.user;
    const firstQuery: any = {
      order_type: {
        $in: [
          SaleOrderTypeEnum.UserItem,
          SaleOrderTypeEnum.Supply,
        ]
      },
      order_status: ctx.state.orderStatus,
      sale_user_id: user._id,
      del_user: {
        $nin: [user._id],
      },
    };

    if (ctx.state.orderStatus === ItemSaleStatusEnum.sell) {
      firstQuery.order_status = {
        $in: [
          ItemSaleStatusEnum.sell,
          ItemSaleStatusEnum.BusinessSell,
        ],
      };
    } else if (ctx.state.orderStatus === ItemSaleStatusEnum.sellOut) {
      firstQuery.order_status = {
        $in: [
          ItemSaleStatusEnum.sellOut,
          ItemSaleStatusEnum.BusinessSellOut,
        ],
      };
    } else if (ctx.state.orderStatus === ItemSaleStatusEnum.selling) {
      firstQuery.order_status = {
        $in: [
          ItemSaleStatusEnum.selling,
        ],
      };
    }

    const projects = {
      order_amount: 1,
      item_name: '$steam_item.item_name',
      icon_url: '$steam_item.icon_url',
      item_id: 1,
      ip_id: {
        $slice: [ '$steam_item.ip_info._id', -1, 1 ],
      },
      market_name: {
        $cond: [
          '$steam_item.market_name',
          '$steam_item.market_name',
          'unx',
        ],
      },
      price: '$steam_item.market_prices',
      buy_user_id: 1,
      order_status: 1,
      nickname: '$users.patbg_detail.nickname',
      avatar: '$users.patbg_detail.avatar',
      is_merchant: '$users.patbg_detail.is_merchant',
      bargain: 1,
      user_item_ids: 1,
    };
    // 调用接口渠道是否小程序
    const steamItemForm = {
      'steam_item.tmt_show': { $ne: false },
    };
    const flag = enableBoxOfWxmini(ctx.headers.client_type, ctx.headers.app_channel, _.get(ctx.app.config, 'custom.sale'));
    if (flag) {
      steamItemForm['steam_item.item_classify_info._id'] = {
        $ne: new ObjectID('65f0108db06abebd5692544e'),
      };
      steamItemForm['steam_item.item_name'] = {
        $not: /.*盲盒.*/i,
      };
    }
    // 检测是否过滤虚拟卡
    const appid = await ctx.service.item.steamItem.checkAppidFilter();
    if (appid) {
      steamItemForm['appid'] = {
        $nin: appid
      }
    }

    const aggregate: any = [
      {
        $match: firstQuery,
      },
      {
        $lookup: {
          from: 'steam_items',
          localField: 'item_id',
          foreignField: '_id',
          as: 'steam_item',
        },
      },
      {
        $match: steamItemForm,
      },
    ];

    if (ctx.state.orderStatus === ItemSaleStatusEnum.sellOut) {
      aggregate.push({
        $lookup: {
          from: 'users',
          localField: 'buy_user_id',
          foreignField: '_id',
          as: 'users',
        },
      });
      aggregate.push({
        $unwind: {
          path: '$users',
          preserveNullAndEmptyArrays: true,
        },
      });
    }

    ctx.logger.debug(`${this.logPrefix} user_sale_order_list`, JSON.stringify(aggregate));

    let saleOrders: SaleOrderEntity[] = [];
    const options = {
      page: ctx.state.queryOptions.page,
      limit: ctx.state.queryOptions.limit,
      sort: { updated_at: -1 },
      project: projects,
    };
    saleOrders = await ctx.service.item.saleOrder.aggregateSaleOrder(aggregate, options);
    if (ctx.state.orderStatus === ItemSaleStatusEnum.sell || ctx.state.orderStatus === ItemSaleStatusEnum.selling) {
      for (const saleOrder of saleOrders) {
        if (saleOrder.bargain) {
          const user = await ctx.service.user.user.queryUsersByRedis(saleOrder.bargain.user_id);
          saleOrder.bargain.nickname = user.patbg_detail.nickname;
          saleOrder.bargain.avatar = user.patbg_detail.avatar;
        }
      }
    }

    // 交易中订单添加
    if (ctx.state.orderStatus === ItemSaleStatusEnum.selling) {
      const saleOrderIds = _.map(saleOrders, '_id');
      const withdrawOrders = await ctx.service.item.itemWithdrawOrder.queryItemWithdrawOrders({
        'extends.items.sale_order_id': {
          $in: saleOrderIds,
        },
        status: ItemWithdrawOrderStatusEnum.WaitForPay,
        'extends.is_split_parent_order': {
          $ne: true,
        },
      }, {
        created_at: 1,
        status: 1,
        extends: 1,
      });
      await ctx.service.item.itemWithdrawOrder.setExpireTime(withdrawOrders);
      ctx.logger.debug(`${this.logPrefix} user_sale_order_list withdrawOrders: ${JSON.stringify(withdrawOrders)}`)
      for (const order of withdrawOrders) {
        const ids = _.map(order.extends.items, item => {
          return String(item.sale_order_id);
        });
        for (const saleOrder of saleOrders) {
          const index = _.indexOf(ids, String(saleOrder._id));
          if (index >= 0) {
            _.set(saleOrder, 'expire', order.expire);
          }
          _.set(saleOrder, 'quantity', saleOrder.user_item_ids?.length || 1);
        }
      }
    }

    // 兼容元气出售单
    for (const saleOrder of saleOrders) {
      if (saleOrder?.extends?.platform_source === PlatformSourceEnum.Airmart || _.isEmpty(saleOrder?.icon_url)) {
        // 获取商品名称
        const steamItem = await ctx.service.item.steamItem.querySteamItemsByRedis(saleOrder?.item_id);
        if (steamItem) {
          _.set(saleOrder, 'icon_url', [ steamItem.icon_url ]);
          _.set(saleOrder, 'item_name', [ steamItem.item_name ]);
        }
      }
      if (!_.isEmpty(saleOrder.ip_id)) {
        _.set(saleOrder, 'ip_id', saleOrder.ip_id[0] || '');
      }
    }
    // 卖家昵称脱敏
    for (const saleOrder of saleOrders) {
      if (saleOrder?.nickname) {
        _.set(saleOrder, 'nickname', maskNickname(saleOrder.nickname));
      }
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: saleOrders,
    };

  }

  /**
   * 出售商品详情
   */
  public async sale_detail() {
    const { ctx } = this;

    if (typeof this.validator.sale_detail === 'function') {
      await this.validator.sale_detail();
    }

    const saleOrder = ctx.state.saleOrder;
    // if (orderDetails && orderDetails._id) {
    //   const userId = ctx.service.user.user.getLoginUserId();
    //   if (userId) {
    //     const userItemLikeQuery: UserItemLikesQuery = {
    //       order_id: { $eq: String(orderDetails.order_id) },
    //       user_id: { $eq: userId },
    //     };
    //     const like = await ctx.service.user.userItemLikes.queryUserItemLikes(userItemLikeQuery, { _id: 1 });
    //     if (like) {
    //       orderDetails.like_status = true;
    //     }
    //   }
    // }

    ctx.body = {
      code: 0,
      desc: '',
      data: saleOrder,
    };

  }

  public async cool_down() {
    const { ctx, app } = this;

    const ycConfig = _.get(app.config, 'custom.maintenance_modes.yc', false);
    if (ycConfig) {
      throw new ExError('TMT_YC_DOWN');
    }

    await this.validator.cool_down();

    ctx.body = {
      code: 0,
      desc: '',
      data: ctx.state.cdData,
    };
  }

  public async buy() {
    const { ctx } = this;
    await this.validator.buy();

    const saleOrders = ctx.state.saleOrders;
    const form = ctx.state.form;
    const buy_items = form.buy_items;
    const user = ctx.state.user;

    const items: any[] = [];
    const order_source = buy_items[0].order_source;
    let shouldPayAmount = 0; // 应付金额
    let userItemAmount = 0;
    let quantity = 0; // 总数量
    let fee = 0;

    const steamItemIds = _.map(saleOrders, 'item_id');
    const steamItems = await ctx.service.item.steamItem.querySteamItems({ _id: { $in: steamItemIds } }, ['appid', 'item_name']);
    const steamItemMap = new Map();
    const goodName = new Map();
    if (steamItems && steamItems.length > 0) {
      steamItems.forEach(steamItem => {
        steamItemMap.set(String(steamItem._id), _.get(steamItem, 'appid'));
        goodName.set(String(steamItem._id), _.get(steamItem, 'item_name'));
      });
    }
    let itemWithdrawOrder;
    try {
      // let is_batch_buy = false;
      // if (_.get(saleOrders, 'length', 0) > 1) {
      //   is_batch_buy = true;
      // }
      for (const sale of saleOrders) {
        shouldPayAmount += sale.order_amount;
        userItemAmount += sale.order_amount;
        quantity += 1;

        const tipFee = this.calculateTipFee(sale.order_amount);
        fee += tipFee;
        const appid = steamItemMap.get(String(sale.item_id));
        items.push({
          appid,
          good_name: goodName.get(String(sale.item_id)) || '文潮国际',
          status: ItemWithdrawOrderStatusEnum.WaitForPay,
          item_id: sale.item_id,
          user_item_ids: [sale.user_item_id],
          quantity: 1,
          sell_price: sale.order_amount,
          total_price: sale.order_amount,
          coupon_amount: 0,
          pay_amount: sale.order_amount,
          sale_order_id: sale._id,
          should_pay_amount: shouldPayAmount > 0 ? shouldPayAmount : 1,
          fee: tipFee,
          open_box: sale.open_box,
          nft_box_id: sale.nft_box_id,
          sale_user_id: sale.sale_user_id,
        });
        const order = await ctx.service.item.saleOrder.findOneAndUpdate({
          _id: sale._id,
          order_type: SaleOrderTypeEnum.UserItem,
          order_status: ItemSaleStatusEnum.sell,
          examine_status: ItemExamineStatusEnum.allow,
          sale_user_id: {
            $ne: new ObjectID(user._id),
          },
        }, {
          $set: {
            order_status: ItemSaleStatusEnum.selling,
            buy_user_id: ctx.state.user._id,
          },
        }, { new: true });
        if (!order) {
          throw new ExError('TMT_ORDER_SALE_OF_BUY', 'The sale order status change');
        }
        sale.order_status = ItemSaleStatusEnum.selling;
      }
      const orderGoodName = items.length > 1 ? items[0].good_name + ',...' : items[0].good_name;
      const itemWithdrawOrderForm: ItemWithdrawOrderPartial = {
        status: ItemWithdrawOrderStatusEnum.WaitForPay,
        user_id: user._id,
        sale_user_id: saleOrders.sale_user_id,
        user_item_amount: userItemAmount,
        pay_amount: 0,
        should_pay_amount: shouldPayAmount,
        extends: {
          appid: _.get(_.first(steamItems), 'appid'),
          quantity,
          is_buy_order: true,
          coupon_id: form.coupon_id,
          coupon_amount: 0,
          admin_sale_freight_amount: 0,
          user_type: user.type,
          order_source,
          items,
          order_type: OrderTypeEnum.UserItem,
          tip_fee: fee,
          good_name: orderGoodName,
        },
      };
      if (items.length > 1) {
        _.set(itemWithdrawOrderForm, 'extends.is_split_parent_order', true);
      }

      itemWithdrawOrder = await ctx.service.item.itemWithdrawOrder.createItemWithdrawOrder(itemWithdrawOrderForm);

      const itemList = itemWithdrawOrder.extends.items;
      if (itemList.length > 1) {
        const operations = itemList.map(item => {
          _.set(item, 'pay_amount', item.sell_price);
          const tipFee = this.calculateTipFee(item.sell_price);

          let subWithdrawOrder = _.cloneDeep(itemWithdrawOrder);
          ctx.logger.error(`subWithdrawOrder start ${JSON.stringify(subWithdrawOrder)}`);
          subWithdrawOrder = _.omit(subWithdrawOrder.toObject(), [ '_id', 'updated_at', 'created_at', 'extends.is_split_parent_order', '__v' ]);
          ctx.logger.error(`subWithdrawOrder omit ${JSON.stringify(subWithdrawOrder)}`);

          _.set(subWithdrawOrder, 'good_name', item.good_name);
          _.set(subWithdrawOrder, 'should_pay_amount', item.sell_price);
          _.set(subWithdrawOrder, 'pay_amount', item.sell_price);
          _.set(subWithdrawOrder, 'user_item_amount', item.sell_price);
          _.set(subWithdrawOrder, 'extends.parent_order_id', itemWithdrawOrder._id);
          _.set(subWithdrawOrder, 'extends.quantity', 1);
          _.set(subWithdrawOrder, 'extends.tip_fee', tipFee);
          _.set(subWithdrawOrder, 'extends.items', [ item ]);

          return { insertOne: { document: subWithdrawOrder } };
        });
        const result: any = await ctx.service.item.itemWithdrawOrder.model.bulkWrite(operations);
        ctx.logger.error(`bulkWrite result ${JSON.stringify(result)}`);

        if (result.ok !== 1 || result.nInserted !== items.length) {
          throw new ExError('TMT_SALE_COUNT_INSUFFICIENT', 'the item sale order quantity insufficient');
        }
      }
    } catch (error) {
      throw new ExError(_.get(error, 'code'), _.get(error, 'desc'), _.get(error, 'data'));
    }

    for (const sale of saleOrders) {
      //订单交易
      await ctx.service.match.addMatch(sale,false);
    }

    // 上报创建订单
    ctx.service.rteBg.upload_order(order_source, 1, user._id, itemWithdrawOrder._id, quantity, userItemAmount, 1, 1, 0, itemWithdrawOrder.created_at);
    // 神策埋点,前端协助上报页面
    ctx.service.item.itemWithdrawOrder.entityBuySuccessUrl(itemWithdrawOrder._id, false);

    const data: any = _.pick(itemWithdrawOrder, ['_id', 'created_at']);
    data.pay_amount = shouldPayAmount;
    ctx.body = {
      code: 0,
      desc: '',
      data,
    };
  }

  public async user_item_sale_order_batch_buy() {
    // const { ctx } = this;
    await this.validator.buy();
  }

  /**
   * 批量购买获取匹配出售单数据
   */
  public async get_batch_match_sale_order() {
    const { ctx } = this;
    if (typeof this.validator.get_batch_match_sale_order === 'function') {
      await this.validator.get_batch_match_sale_order();
    }
    const { item_id, buy_price_limit, buy_quantity } = ctx.state.form;
    // 可购买数量
    const canBuyCount = await ctx.service.item.saleOrder.getSellCount(item_id, buy_price_limit);

    let saleTotalData;
    if (buy_quantity) {
      if (buy_quantity > canBuyCount) {
        throw new ExError('TMT_SALE_COUNT_INSUFFICIENT');
      }
      saleTotalData = await ctx.service.item.saleOrder.getSellTotalData(item_id, buy_price_limit, buy_quantity);
    }

    const respData: any = {
      canBuyCount,
      ...saleTotalData,
    };

    ctx.body = {
      code: 0,
      desc: '',
      data: respData,
    };
  }

  /**
   * 提交批量购买请求
   */
  public async submit_batch_request() {
    const { ctx } = this;
    if (typeof this.validator.submit_batch_request === 'function') {
      await this.validator.submit_batch_request();
    }
    const { item_id, buy_price_limit, buy_quantity } = ctx.state.form;
    const batchBuyRequestForm: BatchBuyRequestPartial = {
      item_id,
      buy_price_limit,
      buy_quantity,
    };
    const batchBuyRequest = await ctx.service.item.batchBuyRequest.createBatchBuyRequest(batchBuyRequestForm);
    ctx.body = {
      code: 0,
      desc: '',
      data: batchBuyRequest,
    };
  }

  public async pre_submit_batch_order() {
    const { ctx } = this;
    if (typeof this.validator.pre_submit_batch_order === 'function') {
      await this.validator.pre_submit_batch_order();
    }
    const batchBuyRequest = ctx.state.batchBuyRequest;
    const batchBuyRequestData = _.pick(batchBuyRequest, [ 'item_id', 'buy_price_limit', 'buy_quantity' ]);
    _.set(batchBuyRequestData, 'batch_buy_request_id', batchBuyRequest._id);
    const totalData = ctx.state.totalData;

    const query = {
      _id: batchBuyRequest.item_id,
    };
    const steamItem = await ctx.service.item.steamItem.querySteamItem(query, [ 'icon_url', 'item_name' ]);
    const respData = {
      ...batchBuyRequestData,
      ...totalData,
    };
    _.merge(respData, _.pick(steamItem, [ 'icon_url', 'item_name' ]));
    ctx.body = {
      code: 0,
      desc: '',
      data: respData,
    };
  }

  //废弃不用
  public async submit_batch_buy() {
    const { ctx } = this;
    if (typeof this.validator.submit_batch_buy === 'function') {
      await this.validator.submit_batch_buy();
    }

    const { form, user, batchBuyRequest } = ctx.state;
    const { item_id, buy_price_limit, buy_quantity } = batchBuyRequest;
    const saleOrders = await ctx.service.item.saleOrder.getSellData(item_id, buy_price_limit, buy_quantity);

    const itemList: any[] = [];
    let shouldPayAmount = 0; // 应付金额
    let userItemAmount = 0;
    let quantity = 0; // 总数量
    let fee = 0;

    let itemWithdrawOrder;
    const validSaleOrders: any[] = [];
    const steamItemIds: any[] = [];
    try {
      // 更改出售单状态为交易中
      for (const sale of saleOrders) {
        const saleOrder = await ctx.service.item.saleOrder.model.findOneAndUpdate(
          {
            _id: sale._id,
            order_type: SaleOrderTypeEnum.UserItem,
            order_status: ItemSaleStatusEnum.sell,
            examine_status: ItemExamineStatusEnum.allow,
            sale_user_id: {
              $ne: new ObjectID(user._id),
            },
            order_amount: {
              $lte: buy_price_limit,
            },
          },
          {
            $set: {
              order_status: ItemSaleStatusEnum.selling,
              buy_user_id: ctx.state.user._id,
            },
          }, { new: true }).read(ReadPreference.PRIMARY);
        // 将更新成功出售单加入待处理列表
        if (saleOrder.order_status === ItemSaleStatusEnum.selling) {
          validSaleOrders.push(saleOrder);
          steamItemIds.push(saleOrder.item_id);
        }
      }
      if (!validSaleOrders || validSaleOrders.length === 0) {
        throw new ExError('TMT_SALE_COUNT_INSUFFICIENT', 'the item sale order quantity insufficient');
      }
      const steamItems = await ctx.service.item.steamItem.querySteamItems({ _id: { $in: steamItemIds } }, [ 'appid', 'item_name' ]);
      const steamItemMap = new Map(steamItems.map(steamItem => [ String(steamItem._id), _.pick(steamItem, 'appid', 'item_name') ]));

      const itemIds = validSaleOrders.map(order => order.item_id);
      const issueItems = await ctx.service.item.issueItem.queryIssueItems({ _id: { $in: itemIds } }, [ 'item_id', 'delivery_time' ]);
      const issueItemMap = new Map(issueItems.map(issueItem => [ String(issueItem.item_id), issueItem.delivery_time ]));
      // 处理出售单
      for (const saleOrder of validSaleOrders) {
        shouldPayAmount += saleOrder.order_amount;
        userItemAmount += saleOrder.order_amount;
        quantity += 1;
        const tipFee = this.calculateTipFee(saleOrder.order_amount);
        fee += tipFee;
        const appid = _.get(steamItemMap.get(String(saleOrder.item_id)), 'appid');
        itemList.push({
          appid,
          good_name: _.get(steamItemMap.get(String(saleOrder.item_id)), 'item_name') || '幻潮-幻潮赏',
          status: ItemWithdrawOrderStatusEnum.WaitForPay,
          item_id: saleOrder.item_id,
          user_item_ids: [ saleOrder.user_item_id ],
          quantity: 1,
          sell_price: saleOrder.order_amount,
          total_price: saleOrder.order_amount,
          coupon_amount: 0,
          pay_amount:  saleOrder.order_amount,
          sale_order_id: saleOrder._id,
          should_pay_amount: saleOrder.order_amount > 0 ? saleOrder.order_amount : 1,
          fee: tipFee,
          sale_user_id: saleOrder.sale_user_id,
          delivery_time: issueItemMap.get(String(saleOrder.item_id)) || null,
        });
      }

      const orderGoodName = itemList.length > 1 ? itemList[0].good_name + ',...' : itemList[0].good_name;
      const itemWithdrawOrderForm: ItemWithdrawOrderPartial = {
        good_name: orderGoodName,
        status: ItemWithdrawOrderStatusEnum.WaitForPay,
        user_id: user._id,
        user_item_amount: userItemAmount,
        pay_amount: 0,
        should_pay_amount: shouldPayAmount,
        extends: {
          appid: _.get(_.first(steamItems), 'appid'),
          batch_id: batchBuyRequest._id,
          quantity,
          is_buy_order: true,
          coupon_id: form.coupon_id,
          coupon_amount: 0,
          admin_sale_freight_amount: 0,
          user_type: user.type,
          items: itemList,
          order_type: OrderTypeEnum.UserItem,
          tip_fee: fee,
        },
      };
      if (itemList.length > 1) {
        _.set(itemWithdrawOrderForm, 'extends.is_split_parent_order', true);
      }
      itemWithdrawOrder = await ctx.service.item.itemWithdrawOrder.createItemWithdrawOrder(itemWithdrawOrderForm);

      const items = itemWithdrawOrder.extends.items;
      // item超过1才需要拆单
      if (items.length > 1) {
        const operations = items.map(item => {
          _.set(item, 'pay_amount', item.sell_price);
          const tipFee = this.calculateTipFee(item.sell_price);

          let subWithdrawOrder = _.cloneDeep(itemWithdrawOrder);
          ctx.logger.error(`subWithdrawOrder start ${JSON.stringify(subWithdrawOrder)}`);
          subWithdrawOrder = _.omit(subWithdrawOrder.toObject(), [ '_id', 'updated_at', 'created_at', 'extends.is_split_parent_order', '__v' ]);
          ctx.logger.error(`subWithdrawOrder omit ${JSON.stringify(subWithdrawOrder)}`);

          _.set(subWithdrawOrder, 'good_name', item.good_name);
          _.set(subWithdrawOrder, 'should_pay_amount', item.sell_price);
          _.set(subWithdrawOrder, 'pay_amount', item.sell_price);
          _.set(subWithdrawOrder, 'user_item_amount', item.sell_price);
          _.set(subWithdrawOrder, 'extends.parent_order_id', itemWithdrawOrder._id);
          _.set(subWithdrawOrder, 'extends.quantity', 1);
          _.set(subWithdrawOrder, 'extends.tip_fee', tipFee);
          _.set(subWithdrawOrder, 'extends.items', [ item ]);

          return { insertOne: { document: subWithdrawOrder } };
        });
        const result: any = await ctx.service.item.itemWithdrawOrder.model.bulkWrite(operations);
        ctx.logger.error(`bulkWrite result ${JSON.stringify(result)}`);

        if (result.ok !== 1 || result.nInserted !== items.length) {
          throw new ExError('TMT_SALE_COUNT_INSUFFICIENT', 'the item sale order quantity insufficient');
        }
      }
    } catch (error) {
      throw new ExError(_.get(error, 'code'), _.get(error, 'desc'), _.get(error, 'data'));
    }

    for (const sale of validSaleOrders) {
      await ctx.service.match.addMatch(sale);
    }

    const data: any = _.pick(itemWithdrawOrder, [ '_id', 'created_at' ]);
    data.pay_amount = shouldPayAmount;
    ctx.body = {
      code: 0,
      desc: '',
      data,
    };
  }

  private calculateTipFee(amount) {
    const { ctx } = this;
    const tradeFee = _.get(ctx.app.config, 'custom.trade_fee', {});
    let tip = 0;
    if (tradeFee) {
      tip = _.get(tradeFee, 'ratio', 0) / 100;
    }
    if (tip <= 0) {
      // 最低收取
      tip = Number(0.03);
    }
    return _.ceil(amount * tip);
  }

  /**
   * 删除出售单
   */
  public async del_sale_order() {
    const { ctx } = this;
    if (typeof this.validator.del_sale_order === 'function') {
      await this.validator.del_sale_order();
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: 'success',
    };
  }

  /**
   * 个人页面接口
   */
  public async sale_user_page() {
    const { ctx } = this;

    await this.validator.sale_user_page();

    const userId = ctx.state.userId;
    const user = ctx.state.user;
    const data: any = {};
    const query: any = {
      sale_user_id: userId,
      order_status: {
        $in: [
          ItemSaleStatusEnum.sell,
          ItemSaleStatusEnum.selling,
        ],
      },
      examine_status: ItemExamineStatusEnum.allow,
      order_type: SaleOrderTypeEnum.UserItem,
    };
    // 获取用户信息
    const theUser = await ctx.service.user.user.queryUserById(userId);
    data.avatar = _.get(theUser, 'patbg_detail.avatar');
    data.nickname = _.get(theUser, 'patbg_detail.nickname');
    data.is_merchant = _.get(theUser, 'patbg_detail.is_merchant');
    // 获取与当前用户的关注关系
    data.follow_status = UserForkStatusEnum.UnFork;
    if (user) {
      if (String(user._id) === String(userId)) {
        query.examine_status = {
          $in: [
            ItemExamineStatusEnum.allow,
            ItemExamineStatusEnum.waiting,
          ],
        };
      }
      const userFollow = await ctx.service.user.userFollow.queryUserFollow({
        user_id: userId,
      });
      if (userFollow) {
        const fansUserList = userFollow.fans_user_list;
        const idolUserList = userFollow.idol_user_list;

        if (fansUserList) {
          for (const fans of fansUserList) {
            if (String(user._id) === String(fans)) {
              data.follow_status = UserForkStatusEnum.Fork;
              break;
            }
          }
        }

        if (idolUserList) {
          for (const idol of idolUserList) {
            if (String(user._id) === String(idol)) {
              if (data.follow_status === UserForkStatusEnum.Fork) {
                data.follow_status = UserForkStatusEnum.Follow;
              } else {
                data.follow_status = UserForkStatusEnum.Fans;
              }
              break;
            }
          }
        }
      }
    }
    let saleOrders: SaleOrderEntity[] = [];
    if (_.get(theUser, 'is_privacy', false)) {
      saleOrders = [];
    } else {
      // 调用接口渠道是否小程序
      const steamItemFrom = {}
      const flag = enableBoxOfWxmini(ctx.headers.client_type, ctx.headers.app_channel, _.get(ctx.app.config, 'custom.sale'));
      if (flag) {
        steamItemFrom['steam_item.item_classify_info._id'] = {
          $ne: new ObjectID('65f0108db06abebd5692544e'),
        };
        steamItemFrom['steam_item.item_name'] = {
          $not: /.*盲盒.*/i,
        }
      }
      // 检测是否过滤虚拟卡
      const appid = await ctx.service.item.steamItem.checkAppidFilter();
      if (appid) {
        steamItemFrom['appid'] = {
          $nin: appid
        }
      }
      // 获取用户出售中的物品列表
      const projects = {
        _id: 1,
        order_amount: 1,
        item_name: '$steam_item.item_name',
        icon_url: '$steam_item.icon_url',
        order_status: 1,
        is_sell: '$steam_item.extends.is_presell',
        sell_time: '$steam_item.extends.sell_time',
        sell_listings: '$steam_item.market_prices.unx.sell_listings',
      };
      const aggregate = [
        {
          $match: query,
        },
        {
          $lookup: {
            from: 'steam_items',
            localField: 'item_id',
            foreignField: '_id',
            as: 'steam_item',
          },
        },
        {
          $unwind: '$steam_item',
        },
        {
          $match: steamItemFrom
        }
      ];
      ctx.logger.debug(`${this.logPrefix} sale_order_create`, JSON.stringify(aggregate));

      // const saleOrderCount = await ctx.service.item.saleOrder.countAggregateItem(aggregate);
      // if (saleOrderCount > 0) {
      const options = {
        page: ctx.state.queryOptions.page,
        limit: ctx.state.queryOptions.limit,
        sort: {
          created_at: -1,
        },
        project: projects,
      };
      saleOrders = await ctx.service.item.saleOrder.aggregateSaleOrder(aggregate, options);
    }
    // 判断预售商品 和  预售时间
    for (const userItem of saleOrders) {
      userItem.replenish = _.get(userItem, 'sell_listings', 0) <= 0;
      ctx.logger.info('userItem.replenish:' + userItem.replenish);
      const nowTime = new Date().getTime();
      const isExist = _.get(userItem, 'sell_time', 0);
      if (isExist && isExist !== 0) {
        const sellTime = new Date(isExist).getTime();
        ctx.logger.info('nowTime:' + nowTime + '-----' + 'sellTime:' + sellTime);
        if (nowTime >= sellTime) {
          userItem.is_sell = false;
        } else {
          userItem.sell_time = moment(sellTime + 28800).format('yyyy-MM-DD');
        }
      }
      if (!userItem.is_sell) {
        userItem.is_sell = false;
      }
      delete userItem.price;
    }

    data.saleOrders = saleOrders;

    ctx.body = {
      code: 0,
      desc: '',
      data,
    };
  }

  /**
   * 个人页面全选数据
   */
  public async sale_user_page_all() {
    const { ctx } = this;

    await this.validator.sale_user_page_all();

    const userId = ctx.state.userId;
    const user = ctx.state.user;
    const query: any = {
      sale_user_id: userId,
      order_status: ItemSaleStatusEnum.sell,
      examine_status: ItemExamineStatusEnum.allow,
      order_type: SaleOrderTypeEnum.UserItem,
    };

    if (ctx.state.is_first === 1) {
      query.order_status = {
        $in: [
          ItemSaleStatusEnum.sell,
          ItemSaleStatusEnum.selling,
        ],
      };
    }

    if (user) {
      if (String(user._id) === String(userId)) {
        query.examine_status = {
          $in: [
            ItemExamineStatusEnum.allow,
            ItemExamineStatusEnum.waiting,
          ],
        };
      }
    }

    const saleOrders = await ctx.service.item.saleOrder.queryDatas(query);

    const data: any = {
      total: _.get(saleOrders, 'length', 0),
    };

    data.amount = 0;
    if (data.total > 0) {
      let amount = 0;
      for (const saleOrder of saleOrders) {
        amount = amount + saleOrder.order_amount;
      }
      data.amount = amount;
    }

    ctx.body = {
      code: 0,
      desc: '',
      data,
    };
  }

  /**
  * 按价格查询订单
  */
  public async query_sale_order_by_price() {
    const { ctx } = this;
    await this.validator.query_sale_order_by_price();

    const item_id = ctx.state.item_id;
    const order_amount = ctx.state.order_amount;

    const sale_order = await ctx.service.item.saleOrder.queryDatas({
      order_status: ItemSaleStatusEnum.sell,
      examine_status: ItemExamineStatusEnum.allow,
      order_type: SaleOrderTypeEnum.UserItem,
      item_id,
      sale_user_id: {
        $ne: ctx.state.user._id,
      },
      order_amount: {
        $lte: order_amount,
      }
    }, {
      _id: 1,
      order_amount: 1,
    }, {
      sort: {
        order_amount: 1,
      },
      limit: 200,
    });
    ctx.logger.debug(`${this.logPrefix} query_sale_order_by_price query: ${JSON.stringify({
      order_status: ItemSaleStatusEnum.sell,
      examine_status: ItemExamineStatusEnum.allow,
      order_type: SaleOrderTypeEnum.UserItem,
      item_id,
      sale_user_id: {
        $ne: ctx.state.user._id,
      },
      order_amount: {
        $lte: order_amount,
      }
    })}`)

    ctx.body = {
      code: 0,
      desc: '',
      data: sale_order,
    };
  }

  /**
   * 取消出售(交易中)
   */
  public async cancel() {
    const { ctx } = this;
    if (typeof this.validator.cancel === 'function') {
      await this.validator.cancel();
    }
    const saleOrder = ctx.state.saleOrder;
    await ctx.service.item.saleOrder.cancel(saleOrder);

    ctx.body = {
      code: 0,
      desc: '',
      data: saleOrder._id,
    };
  }

  /**
   * 出售限价/在售价/成交价
   */
  public async referencePrice() {
    const { ctx } = this;
    if (typeof this.validator.referencePrice === 'function') {
      await this.validator.referencePrice();
    }
    const form = ctx.state.form;
    ctx.logger.debug(`${this.logPrefix} referencePrice form`, JSON.stringify(form));
    const result = await ctx.service.item.saleOrder.referencePrice(form.item_ids);
    ctx.body = {
      code: 0,
      desc: '',
      data: result,
    };
  }

  /**
   * 高价提醒
   */
  public async highPriceRemind() {
    const { ctx } = this;
    if (typeof this.validator.highPriceRemind === 'function') {
      await this.validator.highPriceRemind();
    }
    const form = ctx.state.form;
    ctx.logger.debug(`${this.logPrefix} highPriceRemind form`, JSON.stringify(form));
    const saleOrder = await ctx.service.item.saleOrder.highPriceRemind(form.item_id, form.price);
    let isExists = false;
    if (saleOrder) {
      isExists = true;
    }
    ctx.body = {
      code: 0,
      desc: '',
      data: isExists,
    };
  }

  /**
   * 批量购买匹配明细（文潮集市）
   */
  public async batchBuyMatchDetail() {
    const { ctx } = this;
    if (typeof this.validator.batchBuyMatchDetail === 'function') {
      await this.validator.batchBuyMatchDetail();
    }
    const { item_id: itemId, buy_price_limit: buyPriceLimit, buy_quantity: buyQuantity } = ctx.state.form;

    const canBuyCount = await ctx.service.item.saleOrder.getSellCount(itemId, buyPriceLimit);
    if (canBuyCount === 0 || buyQuantity > canBuyCount) {
      throw new ExError('TMT_SALE_COUNT_INSUFFICIENT');
    }
    const saleOrders = await ctx.service.item.saleOrder.getSellData(itemId, buyPriceLimit, buyQuantity);
    // 提取出售用户ID
    const userIds = _.map(saleOrders, 'sale_user_id');
    const userInfoMap = await ctx.service.user.user.getUserSimpleInfoMapByUserIds(userIds);
    let totalAmount = 0;
    for (const saleOrder of saleOrders) {
      saleOrder.userInfo = userInfoMap.get(String(saleOrder.sale_user_id));
      totalAmount = totalAmount + saleOrder.order_amount;
    }

    const respData: any = {
      canBuyCount,
      totalAmount,
      saleOrders,
    };

    ctx.body = {
      code: 0,
      desc: '',
      data: respData,
    };
  }

  public async updateSaleOrders(saleOrders, userId, buy_price_limit) {
    const { ctx } = this;
    const validSaleOrderIds: any = [];
    const bulkOps = saleOrders.map(sale => ({
      updateOne: {
        filter: {
          _id: sale._id,
          order_status: ItemSaleStatusEnum.sell,
          order_amount: { $lte: buy_price_limit },
        },
        update: {
          $set: {
            order_status: ItemSaleStatusEnum.selling,
            buy_user_id: userId,
          },
        },
      },
    }));

    const result: any = await ctx.service.item.saleOrder.model.bulkWrite(bulkOps);
    // 打印result
    ctx.logger.info(`updateSaleOrders result ${JSON.stringify(result)}`);
    if (result.nModified === saleOrders.length) {
      // 提取saleOrders ID
      validSaleOrderIds.push(...saleOrders.map(sale => sale._id));
    } else {
      // 查询是否修改成功
      const querySaleOrders = await ctx.service.item.saleOrder.queryDatas({
        _id: { $in: saleOrders.map(sale => sale._id) },
        order_status: ItemSaleStatusEnum.selling,
        buy_user_id: userId,
      });
      validSaleOrderIds.push(...querySaleOrders.map(sale => sale._id));
    }
    return validSaleOrderIds;
  }

  async createWithdrawOrders(validSaleOrders, issueItem, user) {
    const { ctx } = this;
    const itemWithdrawOrders: any[] = [];
    const batchId = new ObjectId();
    for (const validSaleOrder of validSaleOrders) {
      const appid = AppidEnum.Real;
      const goodName = issueItem.item_id ?? '文潮集市';
      const tipFee = this.calculateTipFee(validSaleOrder.order_amount);
      const item = {
        status: ItemWithdrawOrderStatusEnum.WaitForPay,
        item_id: validSaleOrder.item_id,
        user_item_ids: [ validSaleOrder.user_item_id ],
        quantity: 1,
        sell_price: validSaleOrder.order_amount,
        total_price: validSaleOrder.order_amount,
        coupon_amount: 0,
        pay_amount: validSaleOrder.order_amount,
        sale_order_id: validSaleOrder._id,
        should_pay_amount: validSaleOrder.order_amount > 0 ? validSaleOrder.order_amount : 1,
        fee: tipFee,
        sale_user_id: validSaleOrder.sale_user_id,
        delivery_time: issueItem.delivery_time,
      };

      const itemWithdrawOrderForm = {
        good_name: goodName,
        status: ItemWithdrawOrderStatusEnum.WaitForPay,
        user_id: user._id,
        user_item_amount: validSaleOrder.order_amount,
        pay_amount: 0,
        should_pay_amount: validSaleOrder.order_amount,
        extends: {
          appid,
          batch_id: batchId,
          quantity: 1,
          is_buy_order: true,
          coupon_amount: 0,
          admin_sale_freight_amount: 0,
          user_type: user.type,
          items: [ item ],
          order_type: OrderTypeEnum.UserItem,
          tip_fee: tipFee,
        },
      };

      const itemWithdrawOrder: any = await ctx.service.item.itemWithdrawOrder.createItemWithdrawOrder(itemWithdrawOrderForm);
      itemWithdrawOrders.push(itemWithdrawOrder);
    }
    return itemWithdrawOrders;
  }

  // 处理成功的订单
  public async handleSuccessOrders(successItemWithdrawOrders) {
    const { ctx } = this;
    const successResult = await ctx.service.item.itemWithdrawOrder.model.bulkWrite(
      successItemWithdrawOrders.map(itemWithdrawOrder => {
        return {
          updateOne: {
            filter: {
              _id: itemWithdrawOrder.itemWithdrawOrderId,
              status: ItemWithdrawOrderStatusEnum.WaitForShip,
            },
            update: {
              $set: {
                status: ItemWithdrawOrderStatusEnum.Done,
                'extends.items.$[].status': ItemWithdrawOrderStatusEnum.Done,
                'extends.acquire_user_item_id': _.first(itemWithdrawOrder.acquireUserItemIds),
                'extends.chain_status': UpChainStatusEnum.Waiting,
              },
            },
          },
        };
      }),
    );
    ctx.logger.info(`updateItemWithdrawOrder Done result ${JSON.stringify(successResult)}`);
  }

  // 处理失败的订单
  public async handleFailedOrders(failItemWithdrawOrders) {
    const { ctx } = this;
    // 更新 itemWithdrawOrder
    const updateItemWithdrawOrder = itemWithdrawOrder => {
      return {
        updateOne: {
          filter: {
            _id: itemWithdrawOrder.itemWithdrawOrderId,
            status: ItemWithdrawOrderStatusEnum.WaitForPay,
          },
          update: {
            $set: {
              status: ItemWithdrawOrderStatusEnum.Expired,
              'extends.items.$[].status': ItemWithdrawOrderStatusEnum.Expired,
            },
          },
        },
      };
    };

    // 更新 saleOrder
    const updateSaleOrder = itemWithdrawOrder => {
      return {
        updateOne: {
          filter: {
            _id: itemWithdrawOrder.saleOrderId,
            order_status: ItemSaleStatusEnum.selling,
          },
          update: {
            $set: {
              order_status: ItemSaleStatusEnum.sell,
              bargain: null,
            },
          },
        },
      };
    };

    // 批量更新 itemWithdrawOrder
    const failItemWithdrawOrderUpdatesOps = failItemWithdrawOrders.map(updateItemWithdrawOrder);
    const failResult = await ctx.service.item.itemWithdrawOrder.model.bulkWrite(failItemWithdrawOrderUpdatesOps);
    ctx.logger.info(`updateItemWithdrawOrder Expired result ${JSON.stringify(failResult)}`);

    // 批量更新 saleOrder
    const failSaleOrderUpdatesOps = failItemWithdrawOrders.map(updateSaleOrder);
    const failSaleOrderResult = await ctx.service.item.saleOrder.model.bulkWrite(failSaleOrderUpdatesOps);
    ctx.logger.info(`updateSaleOrder Expired result ${JSON.stringify(failSaleOrderResult)}`);
  }

  public async processItemWithdrawOrders(itemWithdrawOrders, validSaleOrderGroup, user) {
    const { ctx } = this;
    const failItemWithdrawOrders: any[] = [];
    const successItemWithdrawOrders: any[] = [];
    const concurrencyLimit = 5;

    const processItemWithdrawOrder = async itemWithdrawOrder => {
      const item = _.first(itemWithdrawOrder.extends?.items);
      const validSaleOrder = _.first(validSaleOrderGroup[item.sale_order_id]);
      try {
        try {
          // 支付扣款
          const payRes: any = await ctx.service.watBg.spdbPayUsedItem(
            new ObjectID(user._id),
            new ObjectID(validSaleOrder.sale_user_id),
            validSaleOrder.order_amount,
            itemWithdrawOrder.extends.tip_fee,
            itemWithdrawOrder._id,
          );
          if (payRes === -1) {
            ctx.logger.error('batchBuyOrder pay unknown err');
            return;
          }
        } catch (err) {
          ctx.logger.error(`batchBuyOrder pay err ${JSON.stringify(err)}`);
          // 判断err.code是否以8020开头
          if (err.code && err.code.startsWith('8020')) {
            failItemWithdrawOrders.push({
              itemWithdrawOrderId: itemWithdrawOrder._id,
              saleOrderId: validSaleOrder._id,
            });
          }
          return;
        }
        // 更新为待发货
        const updatedItemWithdrawOrder = await ctx.service.item.itemWithdrawOrder.findOneAndUpdate(
          {
            _id: itemWithdrawOrder._id,
            user_id: user._id,
            status: ItemWithdrawOrderStatusEnum.WaitForPay,
          },
          {
            $set: {
              status: ItemWithdrawOrderStatusEnum.WaitForShip,
              pay_amount: validSaleOrder.order_amount,
              'extends.recharge_time': new Date(),
              'extends.recharge_method': RechargeMethodEnum.YopBalancePay,
              'extends.items.$[ele].status': ItemWithdrawOrderStatusEnum.WaitForShip,
              'extends.remove_time': new Date(),
              'extends.payer': 4,
              'extends.is_bull': false,
            },
          },
          {
            arrayFilters: [{ 'ele.status':  ItemWithdrawOrderStatusEnum.WaitForPay }],
            new: true,
          },
        );
        // 更新出售单为已售出
        const saleOrder = await ctx.service.item.saleOrder.findOneAndUpdate(
          {
            _id: item.sale_order_id,
            order_status: ItemSaleStatusEnum.selling,
            buy_user_id: itemWithdrawOrder.user_id,
          },
          {
            $set: {
              order_status: ItemSaleStatusEnum.sellOut,
              order_tiem: new Date(),
            },
          },
        );

        const acquireUserItemIds = await ctx.service.item.itemWithdrawOrder.transferUserItem(updatedItemWithdrawOrder, [ saleOrder.user_item_id ]);
        successItemWithdrawOrders.push({
          itemWithdrawOrderId: updatedItemWithdrawOrder._id,
          acquireUserItemIds,
        });
      } catch (err) {
        ctx.logger.error(`processItemWithdrawOrder error: ${err.message}`);
      }
    };

    // 使用 Promise.all 控制并发数
    const processQueue = async (tasks, limit) => {
      const results: any[] = [];
      const executing: any[] = [];

      for (const task of tasks) {
        const p = processItemWithdrawOrder(task).then(res => {
          results.push(res);
          return res;
        });

        executing.push(p);

        if (executing.length >= limit) {
          await Promise.all(executing);
          executing.splice(0, executing.length);
        }
      }

      await Promise.all(executing);
      return results;
    };

    const results = await processQueue(itemWithdrawOrders, concurrencyLimit);
    ctx.logger.error(`processItemWithdrawOrder result ${JSON.stringify(results)}`);

    ctx.logger.error('batchBuyOrder successItemWithdrawOrders', JSON.stringify(successItemWithdrawOrders));
    ctx.logger.error('batchBuyOrder failItemWithdrawOrders', JSON.stringify(failItemWithdrawOrders));

    return {
      successItemWithdrawOrders,
      failItemWithdrawOrders,
    };
  }

  /**
   * 批量购买（文潮集市）
   */
  public async batchBuyOrder() {
    const { ctx } = this;
    if (typeof this.validator.batchBuyOrder === 'function') {
      await this.validator.batchBuyOrder();
    }
    const { form, user, issueItem } = ctx.state;
    const { item_id, buy_price_limit, buy_quantity } = form;

    // 同一时间只能购买一笔订单
    const prefix = ctx.app.config.custom.redis.prefix;
    const lockName = `${prefix}:Locks:batch_buy:${String(user._id)}`;
    const lock = await getRedisLock(ctx.app, lockName, 1000 * 30);
    if (!lock) {
      throw new ExError('TMT_PRE_BUY_ORDER_UNFINISHED', '上笔购买订单未完成');
    }
    let successItemWithdrawOrders: any[] = [];
    let failItemWithdrawOrders: any[] = [];
    const validSaleOrders: any[] = [];
    try {
      const saleOrders = await ctx.service.item.saleOrder.getSellData(item_id, buy_price_limit, buy_quantity);
      // 更改出售单状态为交易中
      for (const sale of saleOrders) {
        const saleOrder = await ctx.service.item.saleOrder.model.findOneAndUpdate(
          {
            _id: sale._id,
            order_type: SaleOrderTypeEnum.UserItem,
            order_status: ItemSaleStatusEnum.sell,
            examine_status: ItemExamineStatusEnum.allow,
            sale_user_id: {
              $ne: new ObjectID(user._id),
            },
            order_amount: {
              $lte: buy_price_limit,
            },
          },
          {
            $set: {
              order_status: ItemSaleStatusEnum.selling,
              buy_user_id: ctx.state.user._id,
            },
          }, { new: true }).read(ReadPreference.PRIMARY);
        // 将更新成功出售单加入待处理列表
        if (saleOrder.order_status === ItemSaleStatusEnum.selling) {
          validSaleOrders.push(saleOrder);
        }
      }
      if (!validSaleOrders || validSaleOrders.length === 0) {
        throw new ExError('TMT_SALE_COUNT_INSUFFICIENT', 'the item sale order quantity insufficient');
      }
      const validSaleOrderGroup = _.groupBy(validSaleOrders, '_id');
      const itemWithdrawOrders: any[] = await this.createWithdrawOrders(validSaleOrders, issueItem, user);
      const result = await this.processItemWithdrawOrders(itemWithdrawOrders, validSaleOrderGroup, user);
      successItemWithdrawOrders = result.successItemWithdrawOrders;
      failItemWithdrawOrders = result.failItemWithdrawOrders;
    } catch (err) {
      ctx.app.logger.error(`batchBuyOrder fail: ${err.message}`, err);
      if (err.code) {
        throw new ExError(err.code, err.desc, err.data);
      }
      throw new ExError('TMT_FAIL');
    } finally {
      // 更新订单为已完成
      await this.handleSuccessOrders(successItemWithdrawOrders);
      // 更新订单为失效并设置出售单为出售中
      await this.handleFailedOrders(failItemWithdrawOrders);
      await ctx.app.redis.del(lockName);

      const randStr = randomWord(true, 16, 16);
      const kafkaMessage = {
        sale_batch_id: '',
        order_amount: 0,
        buy_user_id: ctx.state.user._id,
        item_id: issueItem.item_id,
        successList: successItemWithdrawOrders,
        uni_id: randStr,
      };
      for (const sale of validSaleOrders) {
        if (failItemWithdrawOrders.find(item => String(item.saleOrderId) === String(sale._id))) {
          continue;
        }
        //批量购买成功的订单
        await ctx.service.match.addMatch(sale,false);

        kafkaMessage.order_amount += Number(sale.order_amount);
        kafkaMessage.sale_batch_id = sale?.extends?.sale_batch_id;
      }

      try {
        if (kafkaMessage.order_amount > 0) {
          // 发送Kafka
          const messages: Message[] = [];
          messages.push({
            value: JSON.stringify(kafkaMessage),
          });
          const batchMsg: TopicMessages[] = [{
            topic: 'user_purchase',
            messages,
          }];
          await this.app.producer.sendBatch(batchMsg);
        }
      } catch (err) {
        ctx.app.logger.error('batchBuyOrder send kafka message fail', err);
      }
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: '',
    };
  }

  /**
   * 批量购买-确认页（文潮集市）
   */
  public async batchBuyConfirm() {
    const { ctx } = this;
    if (typeof this.validator.batchBuyConfirm === 'function') {
      await this.validator.batchBuyConfirm();
    }
    const { item_id: itemId } = ctx.state.form;
    // 当前最低卖单价
    const minSaleAmount = await ctx.service.item.saleOrder.queryJustOne(
      { item_id: itemId, order_status: ItemSaleStatusEnum.sell },
      [ 'order_amount' ],
      { sort: { order_amount: 1 } },
    );
    // 当前最高卖单价
    const maxSaleAmount = await ctx.service.item.saleOrder.queryJustOne(
      { item_id: itemId, order_status: ItemSaleStatusEnum.sell },
      [ 'order_amount' ],
      { sort: { order_amount: -1 } },
    );
    let limitCount = 0;
    // 持仓数量限制判断
    const positionLimitConf = _.get(ctx.app.config.custom, 'position_limit', {});
    const issueItem = await ctx.service.item.issueItem.queryIssueItem({
      item_id: Types.ObjectId(itemId),
    });
    let positionLimitRatio = _.get(positionLimitConf, 'ratio', 0);
    if (issueItem.position_limit && issueItem.position_limit?.enabled) {
      positionLimitRatio = _.get(issueItem.position_limit, 'ratio', 0);
    }
    const issueItemQuantity = issueItem?.quantity;
    const positionLimitCount = Math.floor(issueItemQuantity * (positionLimitRatio / 100));
    if (ctx.state.user.open_info?.open_user_id) {
      const userItemCounts = await ctx.service.ycOpm.countUserItemByItemIds([ String(itemId) ] , ctx.state.user.open_info?.open_user_id, [ UserItemStatusEnum.Owned, UserItemStatusEnum.Saleing ]);
      if (issueItemQuantity && issueItemQuantity > 0) {
        const holderQuantity = _.get(_.first(userItemCounts), 'count', 0);
        limitCount = positionLimitCount - holderQuantity;
      }
    } else {
      limitCount = positionLimitCount;
    }
    if (limitCount < 1) {
      limitCount = 1;
    }
    ctx.body = {
      code: 0,
      desc: '',
      data: {
        min_sale_amount: minSaleAmount ? minSaleAmount.order_amount : 0,
        max_sale_amount: maxSaleAmount ? maxSaleAmount.order_amount : 0,
        limit_count: Math.floor(limitCount),
      },
    };
  }

  /**
   * 获取出售中数量
   */
  public async countSellingSaleOrder() {
    const { ctx } = this;
    let saleOrderCount = 0;
    if (!ctx.state.user) {
      ctx.body = {
        code: 0,
        desc: '',
        data: saleOrderCount,
      };
      return;
    }
    saleOrderCount = await ctx.service.item.saleOrder.count(
      {
        sale_user_id:  ctx.state.user._id,
        order_status: {
          $in: [
            ItemSaleStatusEnum.sell,
            ItemSaleStatusEnum.selling,
          ],
        },
        examine_status: ItemExamineStatusEnum.allow,
        order_type: SaleOrderTypeEnum.UserItem,
      },
    );
    ctx.body = {
      code: 0,
      desc: '',
      data: saleOrderCount,
    };
  }

  /**
   * 物品转移补单
   */
  public async user_item_transfer() {
    const { ctx } = this;
    const body = ctx.request.body;
    const orderId = body.orderId;
    const userItemId = body.userItemId;
    let acquireUserItemIds;
    const successItemWithdrawOrders: any[] = [];
    ctx.logger.info(`${this.logPrefix} user_item_transfer orderId: ${orderId}, userItemId: ${userItemId}`);
    const itemWithdrawOrderEntity = await ctx.service.item.itemWithdrawOrder.queryItemWithdrawOrder({ _id: orderId, status: ItemWithdrawOrderStatusEnum.WaitForShip });
    if (itemWithdrawOrderEntity) {
      acquireUserItemIds = await ctx.service.item.itemWithdrawOrder.transferUserItem(itemWithdrawOrderEntity, [ userItemId ]);
      successItemWithdrawOrders.push({
        itemWithdrawOrderId: itemWithdrawOrderEntity._id,
        acquireUserItemIds,
      });
    }

    await this.handleSuccessOrders(successItemWithdrawOrders);
    ctx.body = {
      code: 0,
      desc: 'success',
      data: { acquireUserItemIds },
    };
  }
}

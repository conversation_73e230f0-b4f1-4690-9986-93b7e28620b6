<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="@localhost">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.53">
    <root id="1">
      <DefaultCasing>exact</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <ServerVersion>8.0.32</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="4" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="6" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="10" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="18" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="20" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="21" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="23" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="24" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="25" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="26" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="27" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="28" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="29" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="30" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="31" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="32" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="33" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="36" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="37" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="38" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="39" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="45" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="47" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="48" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="49" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="50" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="51" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="52" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="55" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="56" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="57" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="58" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="59" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="61" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="67" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="69" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="71" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="73" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="74" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="77" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="79" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="80" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="81" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="82" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="83" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="84" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="85" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="86" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="87" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="89" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="116" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="117" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="118" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="124" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="144" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="145" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="146" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="152" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8mb3_general_ci">
      <Charset>utf8mb3</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="172" parent="1" name="utf8mb3_tolower_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8mb3_bin">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8mb3_unicode_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8mb3_icelandic_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8mb3_latvian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8mb3_romanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="178" parent="1" name="utf8mb3_slovenian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8mb3_polish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8mb3_estonian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8mb3_spanish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8mb3_swedish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8mb3_turkish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8mb3_czech_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8mb3_danish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8mb3_lithuanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8mb3_slovak_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8mb3_spanish2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8mb3_roman_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8mb3_persian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8mb3_esperanto_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8mb3_hungarian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8mb3_sinhala_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8mb3_german2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb3_croatian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb3_unicode_520_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb3_vietnamese_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb3_general_mysql500_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="274" parent="1" name="utf8mb4_nb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="275" parent="1" name="utf8mb4_nb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="276" parent="1" name="utf8mb4_nn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="277" parent="1" name="utf8mb4_nn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="278" parent="1" name="utf8mb4_sr_latn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="279" parent="1" name="utf8mb4_sr_latn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="280" parent="1" name="utf8mb4_bs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="281" parent="1" name="utf8mb4_bs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="282" parent="1" name="utf8mb4_bg_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="283" parent="1" name="utf8mb4_bg_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="284" parent="1" name="utf8mb4_gl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="285" parent="1" name="utf8mb4_gl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="286" parent="1" name="utf8mb4_mn_cyrl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="287" parent="1" name="utf8mb4_mn_cyrl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="288" parent="1" name="mysql">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="289" parent="1" name="information_schema">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="290" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="291" parent="1" name="sys">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="292" parent="1" name="app_service">
      <AutoIntrospectionLevel>3</AutoIntrospectionLevel>
      <LastIntrospectionLevel>3</LastIntrospectionLevel>
      <LastIntrospectionLocalTimestamp>2025-05-29.02:44:45</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="293" parent="1" name="public_service">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <user id="294" parent="1" name="root">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="295" parent="1" name="mysql.infoschema">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="296" parent="1" name="mysql.session">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="297" parent="1" name="mysql.sys">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="298" parent="1" name="root">
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <table id="299" parent="292" name="ann_category">
      <Comment>公告栏目表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="300" parent="292" name="announcement">
      <Comment>公告表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="301" parent="292" name="category">
      <Comment>栏目表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="302" parent="292" name="common_config">
      <Comment>通用配置表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="303" parent="292" name="market_changes">
      <Comment>行情异动表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="304" parent="292" name="market_changes_channel">
      <Comment>行情异动与发布终端关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="305" parent="292" name="market_changes_item">
      <Comment>行情异动与商品关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="306" parent="292" name="new_user">
      <Comment>用户表（新）</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="307" parent="292" name="operation_log">
      <Comment>操作日志表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="308" parent="292" name="redemption_items">
      <Comment>兑换商品表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="309" parent="292" name="redemption_records">
      <Comment>兑换记录表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="310" parent="292" name="ship_manage">
      <Comment>云仓发货管理表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="311" parent="292" name="synthesis">
      <Comment>合成活动表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="312" parent="292" name="synthesis_log">
      <Comment>合成修改操作日志表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="313" parent="292" name="synthesis_materials">
      <Comment>合成材料表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="314" parent="292" name="synthesis_order">
      <Comment>合成订单表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="315" parent="292" name="synthesis_order_detail">
      <Comment>合成订单详情表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb3_unicode_ci</CollationName>
    </table>
    <table id="316" parent="292" name="user_bind">
      <Comment>用户Bind</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="317" parent="292" name="user_bonus_log">
      <Comment>金币流水日志</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="318" parent="292" name="user_login_log">
      <Comment>用户登录日志表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="319" parent="292" name="user_purchase_log">
      <Comment>用户消费记录表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="320" parent="292" name="user_wallet">
      <Comment>用户钱包</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <column id="321" parent="299" name="ann_category_id">
      <AutoIncrement>1916784488032854016</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="322" parent="299" name="name">
      <Comment>栏目名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="323" parent="299" name="text_color">
      <Comment>文字颜色</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>char(7)|0s</StoredType>
    </column>
    <column id="324" parent="299" name="background_color">
      <Comment>背景颜色</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>char(7)|0s</StoredType>
    </column>
    <column id="325" parent="299" name="priority">
      <Comment>优先级，最大值9999</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="326" parent="299" name="status">
      <Comment>状态：-1:已删除 1启用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="327" parent="299" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="328" parent="299" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="329" parent="299" name="is_del">
      <Comment>是否删除【0-&gt;未删除; 1-&gt;删除】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="330" parent="299" name="PRIMARY">
      <ColNames>ann_category_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="331" parent="299" name="idx_priority">
      <ColNames>priority</ColNames>
      <Type>btree</Type>
    </index>
    <key id="332" parent="299" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="333" parent="300" name="ann_id">
      <AutoIncrement>1916784748889202688</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="334" parent="300" name="title">
      <Comment>公告标题</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="335" parent="300" name="content">
      <Comment>公告内容</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="336" parent="300" name="category_id">
      <Comment>栏目ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="337" parent="300" name="priority">
      <Comment>优先级</Comment>
      <Position>5</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="338" parent="300" name="status">
      <Comment>状态：1待发布 2定时中 3已发布 4已下架</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="339" parent="300" name="publish_time">
      <Comment>发布时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="340" parent="300" name="item_ids">
      <Comment>关联商品挂牌编码</Comment>
      <Position>8</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="341" parent="300" name="channel_ids">
      <Comment>发布终端列表</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="342" parent="300" name="publish_type">
      <Comment>发布方式：1立即发布 2定时发布</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="343" parent="300" name="created_by">
      <Comment>创建人</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>char(24)|0s</StoredType>
    </column>
    <column id="344" parent="300" name="ad_ids">
      <Comment>广告商ids，关联tmt-ad_id</Comment>
      <Position>12</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="345" parent="300" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="346" parent="300" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="347" parent="300" name="PRIMARY">
      <ColNames>ann_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="348" parent="300" name="idx_category_id">
      <ColNames>category_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="349" parent="300" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="350" parent="300" name="idx_publish_time">
      <ColNames>publish_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="351" parent="300" name="idx_created_by">
      <ColNames>created_by</ColNames>
      <Type>btree</Type>
    </index>
    <index id="352" parent="300" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="353" parent="300" name="idx_item_ids">
      <ColNames>&#x1b;(cast(json_extract(`item_ids`,_utf8mb4&apos;$[*]&apos;) as char(24) array))</ColNames>
      <Comment>支持MEMBER OF/JSON_CONTAINS查询</Comment>
      <Type>btree</Type>
    </index>
    <key id="354" parent="300" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="355" parent="301" name="category_id">
      <AutoIncrement>1924756425376325632</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="356" parent="301" name="relate_type">
      <Comment>关联类型，1行情异动</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="357" parent="301" name="name">
      <Comment>名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="358" parent="301" name="text_color">
      <Comment>文字颜色</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>char(7)|0s</StoredType>
    </column>
    <column id="359" parent="301" name="background_color">
      <Comment>背景颜色</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>char(7)|0s</StoredType>
    </column>
    <column id="360" parent="301" name="priority">
      <Comment>优先级，最大值9999</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="361" parent="301" name="status">
      <Comment>状态：-1:已删除 0:禁用 1启用</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="362" parent="301" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="363" parent="301" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="364" parent="301" name="is_del">
      <Comment>是否删除【0-&gt;未删除; 1-&gt;删除】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="365" parent="301" name="PRIMARY">
      <ColNames>category_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="366" parent="301" name="idx_cat_status">
      <ColNames>category_id
status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="367" parent="301" name="idx_relate_type">
      <ColNames>relate_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="368" parent="301" name="idx_priority">
      <ColNames>priority</ColNames>
      <Type>btree</Type>
    </index>
    <key id="369" parent="301" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="370" parent="302" name="id">
      <Comment>主键</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="371" parent="302" name="config_key">
      <Comment>key</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="372" parent="302" name="config_value">
      <Comment>value</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="373" parent="302" name="remark">
      <Comment>备注</Comment>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="374" parent="302" name="created_by">
      <Comment>创建人</Comment>
      <Position>5</Position>
      <StoredType>varchar(24)|0s</StoredType>
    </column>
    <column id="375" parent="302" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="376" parent="302" name="updated_by">
      <Comment>更新人</Comment>
      <Position>7</Position>
      <StoredType>varchar(24)|0s</StoredType>
    </column>
    <column id="377" parent="302" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP(3)</OnUpdate>
      <Position>8</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="378" parent="302" name="is_del">
      <Comment>是否删除【0-&gt;未删除; 1-&gt;删除】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="379" parent="302" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="380" parent="302" name="unx_config_key">
      <ColNames>config_key</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="381" parent="302" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="382" parent="302" name="unx_config_key">
      <UnderlyingIndexName>unx_config_key</UnderlyingIndexName>
    </key>
    <column id="383" parent="303" name="market_changes_id">
      <AutoIncrement>1924792877879472128</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="384" parent="303" name="title">
      <Comment>标题</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="385" parent="303" name="content">
      <Position>3</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="386" parent="303" name="category_id">
      <Comment>栏目ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="387" parent="303" name="status">
      <Comment>状态：1待发布 2定时中 3已发布 4已下架</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="388" parent="303" name="publish_time">
      <Comment>发布时间</Comment>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="389" parent="303" name="publish_type">
      <Comment>发布方式：1立即发布 2定时发布</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="390" parent="303" name="message_push">
      <Comment>消息推送【0-&gt;不推送; 1-&gt;推送】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="391" parent="303" name="created_by">
      <Comment>创建人</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>char(24)|0s</StoredType>
    </column>
    <column id="392" parent="303" name="updated_by">
      <Comment>更新人</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>char(24)|0s</StoredType>
    </column>
    <column id="393" parent="303" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="394" parent="303" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="395" parent="303" name="PRIMARY">
      <ColNames>market_changes_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="396" parent="303" name="idx_ft_title_content">
      <ColNames>title
content</ColNames>
      <Type>fulltext</Type>
    </index>
    <index id="397" parent="303" name="idx_cat_status_pub">
      <ColNames>category_id
status
publish_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="398" parent="303" name="idx_category_id">
      <ColNames>category_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="399" parent="303" name="idx_market_changes_status_publish_time">
      <ColNames>status
publish_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="400" parent="303" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="401" parent="303" name="idx_publish_time">
      <ColNames>publish_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="402" parent="303" name="idx_created_by">
      <ColNames>created_by</ColNames>
      <Type>btree</Type>
    </index>
    <index id="403" parent="303" name="idx_updated_by">
      <ColNames>updated_by</ColNames>
      <Type>btree</Type>
    </index>
    <index id="404" parent="303" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="405" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="406" parent="304" name="market_changes_id">
      <Comment>行情异动ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="407" parent="304" name="channel_id">
      <Comment>发布终端ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="408" parent="304" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="409" parent="304" name="PRIMARY">
      <ColNames>market_changes_id
channel_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="410" parent="304" name="idx_mcc_channel_id">
      <ColNames>channel_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="411" parent="304" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="412" parent="305" name="market_changes_id">
      <Comment>行情异动ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="413" parent="305" name="item_id">
      <Comment>商品ID (商品挂牌编码)</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="414" parent="305" name="item_name">
      <Comment>商品名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="415" parent="305" name="image_url">
      <Comment>商品图片</Comment>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="416" parent="305" name="price_changes">
      <Comment>价格变动</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="417" parent="305" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="418" parent="305" name="PRIMARY">
      <ColNames>market_changes_id
item_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="419" parent="305" name="idx_mci_item_id">
      <ColNames>item_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="420" parent="305" name="idx_ft_item_name">
      <ColNames>item_name</ColNames>
      <Type>fulltext</Type>
    </index>
    <key id="421" parent="305" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="422" parent="306" name="id">
      <AutoIncrement>431</AutoIncrement>
      <Comment>Id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="423" parent="306" name="user_id">
      <Comment>用户id</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </column>
    <column id="424" parent="306" name="mobile_phone">
      <Comment>手机号号码</Comment>
      <Position>3</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="425" parent="306" name="nickname">
      <Comment>昵称</Comment>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="426" parent="306" name="id_card">
      <Comment>身份证号码</Comment>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="427" parent="306" name="avatar">
      <Comment>用户头像</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="428" parent="306" name="real_name">
      <Comment>真实姓名</Comment>
      <Position>7</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="429" parent="306" name="invite_code">
      <Comment>邀请码</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(16)|0s</StoredType>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </column>
    <column id="430" parent="306" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="431" parent="306" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP(3)</OnUpdate>
      <Position>10</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="432" parent="306" name="is_del">
      <Comment>是否删除【0-&gt;未删除; 1-&gt;删除】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="433" parent="306" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="434" parent="306" name="idx_user">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="435" parent="306" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="436" parent="306" name="idx_user">
      <UnderlyingIndexName>idx_user</UnderlyingIndexName>
    </key>
    <column id="437" parent="307" name="operation_log_id">
      <AutoIncrement>30</AutoIncrement>
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="438" parent="307" name="relate_id">
      <Comment>关联id</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="439" parent="307" name="relate_type">
      <Comment>关联类型，1公告</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="440" parent="307" name="action">
      <Comment>操作类型，1创建，2修改，3删除，4发布，5下架，6 同步媒体</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="441" parent="307" name="after_content">
      <Comment>操作后内容</Comment>
      <Position>5</Position>
      <StoredType>text|0s</StoredType>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </column>
    <column id="442" parent="307" name="before_content">
      <Comment>操作前内容</Comment>
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </column>
    <column id="443" parent="307" name="operated_by">
      <Comment>操作人 id</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>char(24)|0s</StoredType>
    </column>
    <column id="444" parent="307" name="operated_at">
      <Comment>操作时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="445" parent="307" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="446" parent="307" name="PRIMARY">
      <ColNames>operation_log_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="447" parent="307" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="448" parent="308" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键 ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="449" parent="308" name="product_id">
      <Comment>商品 ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>char(50)|0s</StoredType>
    </column>
    <column id="450" parent="308" name="product_sku">
      <Comment>商品 SKU</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>char(50)|0s</StoredType>
    </column>
    <column id="451" parent="308" name="product_name">
      <Comment>商品名称</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="452" parent="308" name="product_image">
      <Comment>商品图片 URL</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="453" parent="308" name="redemption_price">
      <Comment>兑换单价</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="454" parent="308" name="stock">
      <Comment>可兑换库存</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="455" parent="308" name="redeemed_quantity">
      <Comment>已兑换数量</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="456" parent="308" name="limit_per_user">
      <Comment>每人限兑数量</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="457" parent="308" name="level">
      <Comment>优先级（数值越大优先级越高）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="458" parent="308" name="status">
      <Comment>状态（0: 下架, 1: 上架, 2: 售罄）</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="459" parent="308" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="460" parent="308" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>13</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <index id="461" parent="308" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="462" parent="308" name="idx_product_id">
      <ColNames>product_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="463" parent="308" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="464" parent="308" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="465" parent="309" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键 ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="466" parent="309" name="order_number">
      <Comment>兑换单号（唯一标识订单）</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="467" parent="309" name="redemption_time">
      <Comment>兑换时间</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="468" parent="309" name="redemption_item_id">
      <Comment>兑换ID（字符串）</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>char(24)|0s</StoredType>
    </column>
    <column id="469" parent="309" name="product_id">
      <Comment>商品 ID（字符串）</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>char(24)|0s</StoredType>
    </column>
    <column id="470" parent="309" name="user_id">
      <Comment>用户 ID</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>char(24)|0s</StoredType>
    </column>
    <column id="471" parent="309" name="user_nickname">
      <Comment>用户昵称</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="472" parent="309" name="mobile_phone">
      <Comment>用户手机号</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="473" parent="309" name="redemption_price">
      <Comment>兑换单价</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="474" parent="309" name="quantity">
      <Comment>兑换数量</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="475" parent="309" name="points_used">
      <Comment>消耗积分</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int unsigned|0s</StoredType>
    </column>
    <column id="476" parent="309" name="total_cost">
      <Comment>合计成本</Comment>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="477" parent="309" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="478" parent="309" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>14</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <index id="479" parent="309" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="480" parent="309" name="order_number">
      <ColNames>order_number</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="481" parent="309" name="idx_order_number">
      <ColNames>order_number</ColNames>
      <Type>btree</Type>
    </index>
    <index id="482" parent="309" name="idx_redemption_item_id">
      <ColNames>redemption_item_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="483" parent="309" name="idx_product_id">
      <ColNames>product_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="484" parent="309" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="485" parent="309" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="486" parent="309" name="order_number">
      <UnderlyingIndexName>order_number</UnderlyingIndexName>
    </key>
    <column id="487" parent="310" name="ship_manage_id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="488" parent="310" name="yc_id">
      <Comment>云仓订单 id</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="489" parent="310" name="withdraw_type">
      <Comment>提货方式：1：手动提货 2：强制发货</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="490" parent="310" name="order_type">
      <Comment>订单来源：1：云仓提货 2：文潮提货</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="491" parent="310" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="492" parent="310" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="493" parent="310" name="PRIMARY">
      <ColNames>ship_manage_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="494" parent="310" name="uniq_yc_id">
      <ColNames>yc_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="495" parent="310" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="496" parent="310" name="uniq_yc_id">
      <UnderlyingIndexName>uniq_yc_id</UnderlyingIndexName>
    </key>
    <column id="497" parent="311" name="id">
      <AutoIncrement>64</AutoIncrement>
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="498" parent="311" name="activity_code">
      <Comment>活动编码</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="499" parent="311" name="title">
      <Comment>活动名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="500" parent="311" name="activity_type">
      <Comment>合成类型【1-优先购权益；2-商品】</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>4</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="501" parent="311" name="cover_url">
      <Comment>封面图</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="502" parent="311" name="status">
      <Comment>状态【-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束】</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="503" parent="311" name="item_id">
      <Comment>合成物品id(商品/优先购)</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="504" parent="311" name="item_title">
      <Comment>合成物品名称(商品/优先购)</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="505" parent="311" name="item_image_url">
      <Comment>合成物品图片</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="506" parent="311" name="user_limit">
      <Comment>每人限合(0不限制)</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="507" parent="311" name="critical_material_limit">
      <Comment>关键材料最小值</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="508" parent="311" name="complete_user_num">
      <Comment>已合人数(按用户去重)</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="509" parent="311" name="stock">
      <Comment>剩余库存</Comment>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="510" parent="311" name="total_stock">
      <Comment>总库存</Comment>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="511" parent="311" name="stock_display">
      <Comment>剩余库存是否显示【1:显示;2:不显示】</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>15</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="512" parent="311" name="start_time">
      <Comment>开始时间</Comment>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="513" parent="311" name="end_time">
      <Comment>结束时间</Comment>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="514" parent="311" name="activity_desc">
      <Comment>说明</Comment>
      <Position>18</Position>
      <StoredType>text|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="515" parent="311" name="created_by">
      <Comment>创建人</Comment>
      <Position>19</Position>
      <StoredType>varchar(24)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="516" parent="311" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="517" parent="311" name="updated_by">
      <Comment>更新人</Comment>
      <Position>21</Position>
      <StoredType>varchar(24)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="518" parent="311" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP(3)</OnUpdate>
      <Position>22</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="519" parent="311" name="is_del">
      <Comment>是否删除【0-&gt;未删除; 1-&gt;删除】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="520" parent="311" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="521" parent="311" name="uk_activity_code">
      <ColNames>activity_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="522" parent="311" name="idx_type_status">
      <ColNames>activity_type
status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="523" parent="311" name="idx_status_del_time">
      <ColNames>status
is_del
start_time
end_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="524" parent="311" name="idx_item_id">
      <ColNames>item_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="525" parent="311" name="idx_item_title">
      <ColNames>item_title</ColNames>
      <Type>btree</Type>
    </index>
    <key id="526" parent="311" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="527" parent="311" name="uk_activity_code">
      <UnderlyingIndexName>uk_activity_code</UnderlyingIndexName>
    </key>
    <column id="528" parent="312" name="id">
      <AutoIncrement>181</AutoIncrement>
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="529" parent="312" name="synthesis_id">
      <Comment>合成活动id(synthesis.id)</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="530" parent="312" name="action">
      <Comment>操作类型，1创建，2修改，3删除，4上架，5下架</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="531" parent="312" name="content">
      <Comment>修改内容</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="532" parent="312" name="old_content">
      <Comment>旧内容</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="533" parent="312" name="created_by">
      <Comment>创建人</Comment>
      <Position>6</Position>
      <StoredType>varchar(24)|0s</StoredType>
    </column>
    <column id="534" parent="312" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="535" parent="312" name="updated_by">
      <Comment>更新人</Comment>
      <Position>8</Position>
      <StoredType>varchar(24)|0s</StoredType>
    </column>
    <column id="536" parent="312" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP(3)</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="537" parent="312" name="is_del">
      <Comment>是否删除【0-&gt;未删除; 1-&gt;删除】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="538" parent="312" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="539" parent="312" name="idx_synthesis_id">
      <ColNames>synthesis_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="540" parent="312" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="541" parent="312" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="542" parent="313" name="id">
      <AutoIncrement>141</AutoIncrement>
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="543" parent="313" name="synthesis_id">
      <Comment>合成活动id(synthesis.id)</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="544" parent="313" name="materials_type">
      <Comment>材料类型【1-核心材料；2-关键材料】</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>3</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="545" parent="313" name="materials_data">
      <Comment>材料内容</Comment>
      <Position>4</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="546" parent="313" name="limit_qty">
      <Comment>限制数量(0全部集齐)</Comment>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="547" parent="313" name="created_by">
      <Comment>创建人</Comment>
      <Position>6</Position>
      <StoredType>varchar(24)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="548" parent="313" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="549" parent="313" name="updated_by">
      <Comment>更新人</Comment>
      <Position>8</Position>
      <StoredType>varchar(24)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="550" parent="313" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP(3)</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="551" parent="313" name="is_del">
      <Comment>是否删除【0-&gt;未删除; 1-&gt;删除】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="552" parent="313" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="553" parent="313" name="idx_synthesis_id_materials_type">
      <ColNames>synthesis_id
materials_type</ColNames>
      <Type>btree</Type>
    </index>
    <key id="554" parent="313" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="555" parent="314" name="id">
      <AutoIncrement>258</AutoIncrement>
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="556" parent="314" name="order_id">
      <Comment>订单号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="557" parent="314" name="synthesis_id">
      <Comment>合成活动id(synthesis.id)</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="558" parent="314" name="synthesis_title">
      <Comment>合成活动名称(synthesis.title)</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="559" parent="314" name="item_id">
      <Comment>合成物品id(商品/优先购)</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="560" parent="314" name="item_title">
      <Comment>合成物品名称(商品/优先购)</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="561" parent="314" name="item_cover_url">
      <Comment>合成物品图片，没有则为空</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="562" parent="314" name="item_info">
      <Comment>合成物品信息</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="563" parent="314" name="user_id">
      <Comment>用户id</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(24)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="564" parent="314" name="status">
      <Comment>状态【-1:失败;1:待融合;21:已融合(云仓材料消耗成功);91:已完成】</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>10</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="565" parent="314" name="qty">
      <Comment>合成数量</Comment>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="566" parent="314" name="app_channel">
      <Comment>设备渠道</Comment>
      <Position>12</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="567" parent="314" name="app_version">
      <Comment>设备版本号</Comment>
      <Position>13</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="568" parent="314" name="ip">
      <Comment>ip</Comment>
      <Position>14</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="569" parent="314" name="synthesis_time">
      <Comment>合成时间</Comment>
      <Position>15</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="570" parent="314" name="chain_hash">
      <Comment>链hash</Comment>
      <Position>16</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="571" parent="314" name="chain_data_id">
      <Comment>链data_id</Comment>
      <Position>17</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="572" parent="314" name="chain_status">
      <Comment>链状态【0:未上链;1:已上链;2:上链中;3:上链失败】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>18</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="573" parent="314" name="created_by">
      <Comment>创建人</Comment>
      <Position>19</Position>
      <StoredType>varchar(24)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="574" parent="314" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="575" parent="314" name="updated_by">
      <Comment>更新人</Comment>
      <Position>21</Position>
      <StoredType>varchar(24)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="576" parent="314" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP(3)</OnUpdate>
      <Position>22</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="577" parent="314" name="is_del">
      <Comment>是否删除【0-&gt;未删除; 1-&gt;删除】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>23</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="578" parent="314" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="579" parent="314" name="uk_order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="580" parent="314" name="idx_synthesis_id">
      <ColNames>synthesis_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="581" parent="314" name="idx_item_id">
      <ColNames>item_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="582" parent="314" name="idx_item_title">
      <ColNames>item_title</ColNames>
      <Type>btree</Type>
    </index>
    <index id="583" parent="314" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="584" parent="314" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="585" parent="314" name="uk_order_id">
      <UnderlyingIndexName>uk_order_id</UnderlyingIndexName>
    </key>
    <column id="586" parent="315" name="id">
      <AutoIncrement>1044</AutoIncrement>
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="587" parent="315" name="synthesis_order_id">
      <Comment>合成订单id(synthesis_order.id)</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="588" parent="315" name="materials_item_id">
      <Comment>物品id</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(24)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="589" parent="315" name="materials_item_name">
      <Comment>物品名称</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="590" parent="315" name="materials_item_url">
      <Comment>物品图片</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>varchar(1000)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="591" parent="315" name="materials_item_info">
      <Comment>物品信息</Comment>
      <Position>6</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="592" parent="315" name="user_item_id">
      <Comment>用户背包物品id</Comment>
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="593" parent="315" name="created_by">
      <Comment>创建人</Comment>
      <Position>8</Position>
      <StoredType>varchar(24)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="594" parent="315" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="595" parent="315" name="updated_by">
      <Comment>更新人</Comment>
      <Position>10</Position>
      <StoredType>varchar(24)|0s</StoredType>
      <CollationName>utf8mb4_0900_as_ci</CollationName>
    </column>
    <column id="596" parent="315" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP(3)</OnUpdate>
      <Position>11</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="597" parent="315" name="is_del">
      <Comment>是否删除【0-&gt;未删除; 1-&gt;删除】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="598" parent="315" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="599" parent="315" name="idx_synthesis_order_id">
      <ColNames>synthesis_order_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="600" parent="315" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="601" parent="316" name="user_bind_id">
      <AutoIncrement>140</AutoIncrement>
      <Comment>Id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="602" parent="316" name="user_id">
      <Comment>用户id</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </column>
    <column id="603" parent="316" name="target_user_id">
      <Comment>被邀请用户id</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(32)|0s</StoredType>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </column>
    <column id="604" parent="316" name="bind_time">
      <Comment>绑定时间</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="605" parent="316" name="type">
      <Comment>类型 1-邀请绑定</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="606" parent="316" name="created_by">
      <Comment>创建人</Comment>
      <Position>6</Position>
      <StoredType>varchar(24)|0s</StoredType>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </column>
    <column id="607" parent="316" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="608" parent="316" name="updated_by">
      <Comment>更新人</Comment>
      <Position>8</Position>
      <StoredType>varchar(24)|0s</StoredType>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </column>
    <column id="609" parent="316" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP(3)</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="610" parent="316" name="is_del">
      <Comment>是否删除【0-&gt;未删除; 1-&gt;删除】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="611" parent="316" name="PRIMARY">
      <ColNames>user_bind_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="612" parent="316" name="idx_target_user_id">
      <ColNames>user_id
target_user_id
type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="613" parent="316" name="idx_user_id">
      <ColNames>user_id
target_user_id
type</ColNames>
      <Type>btree</Type>
    </index>
    <key id="614" parent="316" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="615" parent="317" name="user_bonus_log_id">
      <AutoIncrement>874</AutoIncrement>
      <Comment>主键id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="616" parent="317" name="user_id">
      <Comment>Id</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </column>
    <column id="617" parent="317" name="name">
      <Comment>名字</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="618" parent="317" name="main_img">
      <Comment>图片</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="619" parent="317" name="source">
      <Comment>来源</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="620" parent="317" name="relate_id">
      <Comment>关联Id</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="621" parent="317" name="amount">
      <Comment>数额</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="622" parent="317" name="receive_status">
      <Comment>1-未解锁 10-待领取 20-已领取 99-过期未领取</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="623" parent="317" name="created_by">
      <Comment>创建人</Comment>
      <Position>9</Position>
      <StoredType>varchar(24)|0s</StoredType>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </column>
    <column id="624" parent="317" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="625" parent="317" name="updated_by">
      <Comment>更新人</Comment>
      <Position>11</Position>
      <StoredType>varchar(24)|0s</StoredType>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </column>
    <column id="626" parent="317" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP(3)</OnUpdate>
      <Position>12</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="627" parent="317" name="is_del">
      <Comment>是否删除【0-&gt;未删除; 1-&gt;删除】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="628" parent="317" name="PRIMARY">
      <ColNames>user_bonus_log_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="629" parent="317" name="idx_relate_id">
      <ColNames>relate_id
source</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="630" parent="317" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="631" parent="317" name="idx_relate_id">
      <UnderlyingIndexName>idx_relate_id</UnderlyingIndexName>
    </key>
    <column id="632" parent="318" name="id">
      <Comment>Id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="633" parent="318" name="user_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(24)|0s</StoredType>
    </column>
    <column id="634" parent="318" name="type">
      <Comment>登录类型</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="635" parent="318" name="version">
      <Comment>版本号</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="636" parent="318" name="address">
      <Comment>地址信息(JSON格式)</Comment>
      <Position>5</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="637" parent="318" name="login_time">
      <Comment>登录时间</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="638" parent="318" name="login_ip">
      <Comment>登录IP</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(15)|0s</StoredType>
    </column>
    <column id="639" parent="318" name="created_at">
      <Comment>创建时间</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="640" parent="318" name="updated_at">
      <Comment>更新时间</Comment>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP(3)</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <index id="641" parent="318" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="642" parent="318" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="643" parent="318" name="idx_login_time">
      <ColNames>login_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="644" parent="318" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="645" parent="319" name="user_purchase_id">
      <AutoIncrement>7864</AutoIncrement>
      <Comment>Id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="646" parent="319" name="user_id">
      <Comment>用户id</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="647" parent="319" name="type">
      <Comment>类型 1-一手消费 2-二手消费</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="648" parent="319" name="relate_order_id">
      <Comment>关联订单Id</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="649" parent="319" name="order_amount">
      <Comment>订单金额</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="650" parent="319" name="purchase_time">
      <Comment>消费时间</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="651" parent="319" name="extra">
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>json|0s</StoredType>
    </column>
    <column id="652" parent="319" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="653" parent="319" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP(3)</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="654" parent="319" name="is_del">
      <Comment>是否删除【0-&gt;未删除; 1-&gt;删除】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="655" parent="319" name="PRIMARY">
      <ColNames>user_purchase_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="656" parent="319" name="idx_user_id">
      <ColNames>user_id
relate_order_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="657" parent="319" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="658" parent="319" name="idx_user_id">
      <UnderlyingIndexName>idx_user_id</UnderlyingIndexName>
    </key>
    <column id="659" parent="320" name="user_wallet_id">
      <AutoIncrement>346</AutoIncrement>
      <Comment>用户id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint unsigned|0s</StoredType>
    </column>
    <column id="660" parent="320" name="user_id">
      <Comment>用户id</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="661" parent="320" name="bonus">
      <Comment>积分</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="662" parent="320" name="total_bonus">
      <Comment>积分</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="663" parent="320" name="created_at">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="664" parent="320" name="updated_at">
      <Comment>更新时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP(3)</OnUpdate>
      <Position>6</Position>
      <StoredType>datetime(3)|0s</StoredType>
    </column>
    <column id="665" parent="320" name="is_del">
      <Comment>是否删除【0-&gt;未删除; 1-&gt;删除】</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="666" parent="320" name="PRIMARY">
      <ColNames>user_wallet_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="667" parent="320" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="668" parent="320" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="669" parent="320" name="idx_user_id">
      <UnderlyingIndexName>idx_user_id</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>
import { Controller } from 'egg';
import * as _ from 'lodash';
import { Document, Types } from 'mongoose';
import { WithdrawOrderEntity, WithdrawOrderQuery, WalletEntity, WithdrawOrderPartial } from 'ExEntitys';
import WithdrawValidator from 'validator/admin/wallet/order/withdraw';
import { urlParse, filingQuerysByMongo, filingFieldsByMongo, filingSortsByMongo, SearchField, UrlParseSearchOp, SearchTypeEnum, setObjByDotKeys } from 'utils/repository';
import { WithdrawOrderStatusEnum } from 'enum/wallet/order/withdraw_order';
import { WalletRoleEnum } from 'enum/wallet/wallet_role';
import { PayTypeEnum } from 'enum/wallet/order/pay_type';
import ExError from 'utils/ex-error/ex_error';
import { CapitalFlowTypeEnum } from 'enum/wallet/capital_flow_type';
import { PayStatusEnum } from 'enum/wallet/order/pay_status';
import { sleep } from 'utils/time';
import { ObjectId } from 'bson';
import { WithdrawTypeEnum } from 'enum/wallet/order/withdraw_type';
// import moment = require('moment');

export default class WithdrawController extends Controller {
  public logPrefix: string = '[controller.admin.wallet.order.withdraw]';
  private validator: WithdrawValidator;

  constructor(ctx) {
    super(ctx);
    this.validator = new WithdrawValidator(ctx);
  }

  public async index() {
    const { ctx } = this;

    if (typeof this.validator.index === 'function') {
      await this.validator.index();
    }

    const allowSearchFields: SearchField[] = [
      {
        key: 'status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'type',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.NE,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'pay_status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'wallet_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'order_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: '_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'created_at',
        allow_ops: [
          UrlParseSearchOp.GTE,
          UrlParseSearchOp.GT,
          UrlParseSearchOp.LTE,
          UrlParseSearchOp.LT,
          UrlParseSearchOp.RANGE,
        ],
        type: SearchTypeEnum.DATE,
      },
    ];
    const allowSortFields = [
      '_id',
    ];
    const allowDisplayFields = [
    ];

    const queryParse = urlParse(ctx.query, {
      search: {
        allowSearchFields,
      },
      sort: {
        allowSortFields,
      },
    });

    let query: WithdrawOrderQuery = {
    };
    // Tips: Object is reference, return value is not necessary
    query = filingQuerysByMongo(query, queryParse);
    const fields: Record<string, any> = filingFieldsByMongo(allowDisplayFields, queryParse);
    let sorts = {
      _id: 'desc',
    };
    sorts = filingSortsByMongo(sorts, queryParse);
    if (query?.type === undefined || query?.type === null) {
      query.type = {
        $ne: WithdrawTypeEnum.CardBalance,
      };
    }

    let orders: WithdrawOrderEntity[] = [];
    const orderCount = await ctx.service.wallet.withdrawOrder.countWithdrawOrder(query);
    if (orderCount > 0) {
      orders = await ctx.service.wallet.withdrawOrder.queryWithdrawOrders(query, fields, {
        page: ctx.state.queryOptions.page,
        limit: ctx.state.queryOptions.limit,
        sort: sorts,
      });
    }

    const rspData = {
      list: orders,
      total: orderCount,
    };

    ctx.body = {
      code: 0,
      desc: '',
      data: rspData,
    };
  }

  public async detail() {
    const { ctx } = this;

    if (typeof this.validator.detail === 'function') {
      await this.validator.detail();
    }

    const order: WithdrawOrderEntity = ctx.state.order;

    let rspData: any = order;
    if (order instanceof Document) {
      rspData = _.omit(order.toObject(), [
        // 'status',
      ]);
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: rspData,
    };
  }

  public async audit() {
    const { ctx } = this;

    if (typeof this.validator.audit === 'function') {
      await this.validator.audit();
    }
    const order: WithdrawOrderEntity = ctx.state.order;

    // ----------- 已审核状态 -----------------------------
    const setAuditedForm = {
      status: WithdrawOrderStatusEnum.Audited,
      operation_admin_id: ctx.state.admin._id,
      operation_data: new Date(),
    };
    const updateOrderRet = await ctx.service.wallet.withdrawOrder.updateWithdrawOrder({
      _id: order._id,
    }, {
      $set: setAuditedForm,
    });
    if (!updateOrderRet) {
      ctx.logger.error(`${this.logPrefix}update order.status${order.status} to ${WithdrawOrderStatusEnum.Audited} failed.`);
      // throw new ExError('WAT_DATA_UPDATE_FAIL', 'update order failed.');
    }
    setObjByDotKeys(order, setAuditedForm);

    // const form: Record<string, any> = ctx.state.form;
    let orderWithdraw;
    if (_.get(order, 'pay_type', 1) === PayTypeEnum.Airmart) {
      const res = await ctx.service.patBg.getUserOauth(ctx.state.userWallet.user_id, 'airmart');
      const userOauth = res.list[0];
      orderWithdraw = await ctx.service.airmartOpm.recharge_of_FTM(userOauth.oauth_id, order.amount - order.handling_fee);
      ctx.service.wallet.withdrawOrder.updateWithdrawOrder({
        _id: order._id,
      }, {
        $set: {
          pay_status: PayStatusEnum.Done,
        },
      });
    } else {
      orderWithdraw = await ctx.service.wallet.withdrawOrder.orderWithdraw(order);
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: orderWithdraw.status,
    };
  }

  public async batch_audit() {
    const { ctx } = this;

    if (typeof this.validator.batch_audit === 'function') {
      await this.validator.batch_audit();
    }
    const orders: WithdrawOrderEntity[] = ctx.state.orders;

    // ----------- 已审核状态 -----------------------------
    const setAuditedForm = {
      status: WithdrawOrderStatusEnum.Audited,
      operation_admin_id: ctx.state.admin._id,
      operation_data: new Date(),
    };
    const withdrawIds: Types.ObjectId[] = ctx.request.body.withdraw_ids;
    const updateOrderRet = await ctx.service.wallet.withdrawOrder.updateWithdrawOrder({
      _id: withdrawIds,
    }, {
      $set: setAuditedForm,
    });
    if (!updateOrderRet) {
      ctx.logger.error(`${this.logPrefix}update order.status to ${WithdrawOrderStatusEnum.Audited} failed.`);
    }
    const wallets: WalletEntity[] = ctx.state.wallets;
    for (const order of orders) {
      setObjByDotKeys(order, setAuditedForm);
      // 从上下文中获取钱包数据，避免再次访问数据库
      for (const wallet of wallets) {
        if (String(wallet._id) === String(order.wallet_id)) {
          ctx.state.userWallet = wallet;
        }
      }
      // const orderWithdraw = await ctx.service.wallet.withdrawOrder.orderWithdraw(order);
      let orderWithdraw;
      if (_.get(order, 'pay_type', 1) === PayTypeEnum.Airmart) {
        const res = await ctx.service.patBg.getUserOauth(ctx.state.userWallet.user_id, 'airmart');
        const userOauth = res.list[0];
        orderWithdraw = await ctx.service.airmartOpm.recharge_of_FTM(userOauth.oauth_id, order.amount - order.handling_fee);
        ctx.service.wallet.withdrawOrder.updateWithdrawOrder({
          _id: order._id,
        }, {
          $set: {
            pay_status: PayStatusEnum.Done,
          },
        });
      } else {
        orderWithdraw = await ctx.service.wallet.withdrawOrder.orderWithdraw(order);
      }
      ctx.logger.info(`订单提现完成：${orderWithdraw}`);
    }
    ctx.body = {
      code: 0,
      desc: '已操作完成',
      data: ctx.request.body.withdraw_ids,
    };
  }

  public async deny() {
    const { ctx } = this;

    if (typeof this.validator.deny === 'function') {
      await this.validator.deny();
    }

    const order: WithdrawOrderEntity = ctx.state.order;
    const userWallet: WalletEntity = ctx.state.userWallet;
    const form: WithdrawOrderPartial = ctx.state.form;

    const balancePoolWallet = await ctx.service.wallet.wallet.getOperatorWallet(WalletRoleEnum.FI_UserBalance);
    let trRet;
    let salefpRet;
    if (_.get(order, 'pay_type', 1) === PayTypeEnum.Airmart) {
      const extend: any = {
        withdraw_order_id: order._id,
      };
      const saleoutItempoolWallet = await ctx.service.wallet.wallet.getOperatorWallet(WalletRoleEnum.FI_SaleoutItem);
      const UserCommWithdrawPoolWallet = await ctx.service.wallet.wallet.getOperatorWallet(WalletRoleEnum.FI_UserWithdrawAirmart);
      trRet = await ctx.service.wallet.walletOperate.transferByDeposit(UserCommWithdrawPoolWallet, userWallet, balancePoolWallet, order.amount, CapitalFlowTypeEnum.UserRecharge, extend);
      // 扣除可提现余额 (记账池余额)
      salefpRet = await ctx.service.wallet.fundsPool.fundsPoolAmountTransfer(saleoutItempoolWallet, saleoutItempoolWallet, userWallet, order.amount);
    } else {
      const userWithdrawPoolWallet = await ctx.service.wallet.wallet.getOperatorWallet(WalletRoleEnum.FI_UserBalanceWithdraw);
      trRet = await ctx.service.wallet.walletOperate.transferByDeposit(userWithdrawPoolWallet, userWallet, balancePoolWallet, order.amount, CapitalFlowTypeEnum.UserRecharge);
    }
    // --------------------------------
    // 更新订单信息
    // --------------------------------

    // 更新订单状态 - 设置为拒绝
    const setRefusedForm: Record<string, any> = {
      status: WithdrawOrderStatusEnum.Refused,
      pay_status: PayStatusEnum.Failed,
      operation_admin_id: ctx.state.admin._id,
      operation_data: new Date(),
      'payment_details.order_receipt.refuse_tr_id': trRet.tr_id,
    };
    if (salefpRet) {
      setRefusedForm['payment_details.order_receipt.saleoutItem_tr_id'] = salefpRet.tr_id;
    }
    if (form.refuse_type) {
      setRefusedForm.refuse_type = form.refuse_type;
    }
    if (form.refuse_reason) {
      setRefusedForm.refuse_reason = form.refuse_reason;
    }
    const updateOrderRet = await ctx.service.wallet.withdrawOrder.updateWithdrawOrder({
      _id: order._id,
    }, {
      $set: setRefusedForm,
    });
    if (!updateOrderRet) {
      const errMsg = `update order.status(${order.status}) failed`;
      ctx.logger.error(`${this.logPrefix}${errMsg}`);
      throw new ExError('WAT_DATA_UPDATE_FAIL', errMsg);
    }
    setObjByDotKeys(order, setRefusedForm);

    ctx.body = {
      code: 0,
      desc: '',
      data: !!updateOrderRet,
    };
  }

  public async batch_deny() {
    const { ctx } = this;
    // 校验逻辑跟通过提现的一样
    if (typeof this.validator.batch_audit === 'function' && typeof this.validator.batch_deny === 'function') {
      await this.validator.batch_deny_valid();
      await this.validator.batch_deny();
    }

    const orders: WithdrawOrderEntity[] = ctx.state.orders;
    const wallets: WalletEntity[] = ctx.state.wallets;
    const form: WithdrawOrderPartial = ctx.state.form;
    const walletMap = new Map();
    for (const wallet of wallets) {
      walletMap.set(String(wallet._id), wallet);
    }
    const balancePoolWallet = await ctx.service.wallet.wallet.getOperatorWallet(WalletRoleEnum.FI_UserBalance);
    const userWithdrawPoolWallet = await ctx.service.wallet.wallet.getOperatorWallet(WalletRoleEnum.FI_UserBalanceWithdraw);
    const saleItemPoolWallet = await ctx.service.wallet.wallet.getOperatorWallet(WalletRoleEnum.FI_SaleoutItem);
    const UserCommWithdrawPoolWallet = await ctx.service.wallet.wallet.getOperatorWallet(WalletRoleEnum.FI_UserWithdrawAirmart);
    const fail: string[] = [];
    let trRet;
    let salefpRet;
    for (const order of orders) {
      const userWallet = walletMap.get(String(order.wallet_id));
      ctx.state.userWallet = userWallet;
      if (_.get(order, 'pay_type', 1) === PayTypeEnum.Airmart) {
        const extend: any = {
          withdraw_order_id: order._id,
        };
        trRet = await ctx.service.wallet.walletOperate.transferByDeposit(UserCommWithdrawPoolWallet, userWallet, balancePoolWallet, order.amount, CapitalFlowTypeEnum.UserRecharge, extend);
        // 扣除可提现余额 (记账池余额)
        salefpRet = await ctx.service.wallet.fundsPool.fundsPoolAmountTransfer(saleItemPoolWallet, saleItemPoolWallet, userWallet, order.amount);
      } else {
        trRet = await ctx.service.wallet.walletOperate.transferByDeposit(userWithdrawPoolWallet, userWallet, balancePoolWallet, order.amount, CapitalFlowTypeEnum.UserRecharge);
      }
      // --------------------------------
      // 更新订单信息
      // --------------------------------
      // 更新订单状态 - 设置为拒绝
      const setRefusedForm: Record<string, any> = {
        status: WithdrawOrderStatusEnum.Refused,
        operation_admin_id: ctx.state.admin._id,
        operation_data: new Date(),
        'payment_details.order_receipt.refuse_tr_id': trRet.tr_id,
      };
      if (form.refuse_type) {
        setRefusedForm.refuse_type = form.refuse_type;
      }
      if (salefpRet) {
        setRefusedForm['payment_details.order_receipt.saleoutItem_tr_id'] = salefpRet.tr_id;
      }
      if (form.refuse_reason) {
        setRefusedForm.refuse_reason = form.refuse_reason;
      }
      const updateOrderRet = await ctx.service.wallet.withdrawOrder.updateWithdrawOrder({
        _id: order._id,
      }, {
        $set: setRefusedForm,
      });
      if (!updateOrderRet) {
        fail.push(String(order._id));
        const errMsg = `update order.status(${order.status}) failed`;
        ctx.logger.error(`${this.logPrefix}${errMsg}`);
      }
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: fail,
    };
  }

  public async retry_withdraw() {
    const { ctx } = this;

    if (typeof this.validator.retry_withdraw === 'function') {
      await this.validator.retry_withdraw();
    }

    const order: WithdrawOrderEntity = ctx.state.order;
    let orderWithdraw;
    if (order.pay_type === PayTypeEnum.Alipay) {
      orderWithdraw = await ctx.service.wallet.withdrawOrder.orderWithdraw(order);
    } else {
      const res = await ctx.service.patBg.getUserOauth(ctx.state.user._id, 'airmart');
      const userOauth = res.list[0];
      try {
        await ctx.service.airmartOpm.recharge_of_FTM(userOauth.oauth_id, order.amount - order.handling_fee);
        ctx.service.wallet.withdrawOrder.updateWithdrawOrder({
          _id: order._id,
        }, {
          $set: {
            pay_status: PayStatusEnum.Done,
          },
        });
      } catch (error) {
        ctx.service.wallet.withdrawOrder.updateWithdrawOrder({
          _id: order._id,
        }, {
          $set: {
            Failed: PayStatusEnum.Failed,
            'payment_details.fail_reason': error,
          },
        });
      }
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: orderWithdraw._id,
    };
  }

  public async spdb_fee() {
    const { ctx } = this;
    const body =  ctx.request.body;

    const fail_ids: any = [];
    for (const orderId of body?.orderIds) {
      try {
        const order = await ctx.service.wallet.withdrawOrder.queryWithdrawOrder({ _id : new ObjectId(orderId), pay_status: PayStatusEnum.Failed });
        if (order) {
          await ctx.service.wallet.withdrawOrder.orderWithdrawFailed(order, true);
        }
        await sleep(1000);
      } catch (err) {
        ctx.logger.error(`${this.logPrefix} spdb_fee fail, order(${orderId})`, err);
        fail_ids.push(orderId);
      }
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: fail_ids,
    };
  }
}

import { Controller } from 'egg';
import * as _ from 'lodash';
import { Document } from 'mongoose';
import { RechargeOrderQuery, RechargeOrderEntity, WalletEntity, RechargeOrderPartial } from 'ExEntitys';
import RechargeValidator from 'validator/admin/wallet/order/recharge';
import {
  urlParse,
  filingQuerysByMongo,
  filingFieldsByMongo,
  filingSortsByMongo,
  SearchField,
  UrlParseSearchOp,
  SearchTypeEnum, filingFieldsByArray
} from 'utils/repository';
import { AmountFloat, IPayerChannelConfigEx } from 'utils/payers/i_pay';
import { CurrencyEnum } from 'enum/wallet/currency';
import { RechargeOrderStatusEnum, RechargeOrderTypeTypeEnum } from 'enum/wallet/order/recharge_order';
import ExError from 'utils/ex-error/ex_error';
import { retryTask } from 'utils/task';
import { orderIdWithRandom } from 'utils/wallet/order';
import { sleep } from 'utils/time';

export default class RechargeController extends Controller {
  public logPrefix: string = '[controller.admin.wallet.order.recharge]';
  private validator: RechargeValidator;

  constructor(ctx) {
    super(ctx);
    this.validator = new RechargeValidator(ctx);
  }

  public async index() {
    const { ctx } = this;

    if (typeof this.validator.index === 'function') {
      await this.validator.index();
    }

    const allowSearchFields: SearchField[] = [
      {
        key: '_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'status',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'wallet_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'created_at',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.GT,
          UrlParseSearchOp.GTE,
          UrlParseSearchOp.LT,
          UrlParseSearchOp.LTE,
        ],
        type: SearchTypeEnum.DATE,
      },
      {
        key: 'order_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'payer_order_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'payer',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
          UrlParseSearchOp.GTE,
        ],
        type: SearchTypeEnum.NUMBER,
      },
      {
        key: 'pay_channel',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'extends.parent_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
      {
        key: 'options.order_type',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.NE,
        ],
        type: SearchTypeEnum.NUMBER,
      },
    ];
    const allowSortFields = [
      '_id',
    ];
    const allowDisplayFields = [
      'status',
      'wallet_id',
      'order_id',
      'payer_order_id',
      'amount',
      'handling_fee',
      'payer',
      'pay_channel',
      'expire_time',
      'order_trx_data',
      'order_receipt',
      'extends',
      'options',
      'created_at',
      'updated_at',
    ];


    const queryParse = urlParse(ctx.query, {
      search: {
        allowSearchFields,
      },
      sort: {
        allowSortFields,
      },
    });

    let query: RechargeOrderQuery = {
    };
    // Tips: Object is reference, return value is not necessary
    query = filingQuerysByMongo(query, queryParse);
    const fields = filingFieldsByMongo(allowDisplayFields, queryParse) as string[];

    let sorts = {
      _id: -1,
    };
    sorts = filingSortsByMongo(sorts, queryParse);
    if (query['options.order_type'] === undefined || query['options.order_type'] === null) {
      query['options.order_type'] = {
        $ne: RechargeOrderTypeTypeEnum.Card_Balance,
      };
    }
    let aggregates: any[] = [
      { $match: query },
      {
        $lookup: {
          from: 'users',
          localField: 'wallet_id',
          foreignField: 'wallet_id',
          as: 'users',
        },
      },
      {
        $unwind: {
          path: '$wallets',
          preserveNullAndEmptyArrays: true, // 防止未匹配数据导致丢失
        },
      },
      {
        $project: filingFieldsByArray({
          user_id: '$users._id',
          nickname: '$users.patbg_detail.nickname',
          mobile_phone: '$users.patbg_detail.mobile_phone',
        }, fields),
      },
      { $sort: sorts },
    ];

    const countAggregates = [...aggregates];

    const orderCount = await ctx.service.wallet.rechargeOrder.countAggregateRechargeOrder(countAggregates);
    let orders: RechargeOrderEntity[] = [];
    const page = Math.max(1, ctx.state.queryOptions.page || 1);
    const limit = ctx.state.queryOptions.limit || 10; // 确保 limit 有默认值
    if (orderCount > 0) {
      const paginatedAggregates = [
        ...aggregates,
        { $skip: (page - 1) * limit },
        { $limit: ctx.state.queryOptions.limit },
      ];
      ctx.logger.info(`${this.logPrefix}paginatedAggregates`, JSON.stringify(paginatedAggregates));
      orders = await ctx.service.wallet.rechargeOrder.aggregateRechargeOrderWithAllowDiskUse(paginatedAggregates);
    }

    const rspData = {
      list: orders,
      total: orderCount,
    };

    ctx.body = {
      code: 0,
      desc: '',
      data: rspData,
    };
  }

  public async detail() {
    const { ctx } = this;

    if (typeof this.validator.detail === 'function') {
      await this.validator.detail();
    }

    const order: RechargeOrderEntity = ctx.state.order;

    let rspData: any = order;
    if (order instanceof Document) {
      rspData = _.omit(order.toObject(), [
        // 'status',
      ]);
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: rspData,
    };
  }

  public async fix_order() {
    const { ctx } = this;

    if (typeof this.validator.detail === 'function') {
      await this.validator.fix_order();
    }

    const orderIds = ctx.state.orderIds;

    const fix_ids: any = [];
    for (const orderId of orderIds) {
      try {
        await ctx.service.wallet.rechargeOrder.deliverOrder(orderId);
        fix_ids.push(orderId);
      } catch (err) {
        ctx.logger.error(`${this.logPrefix} recharge fail, order(${orderId})`, err);
      }
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: fix_ids,
    };
  }

  public async repair_order() {
    const { ctx } = this;

    if (typeof this.validator.repair_order === 'function') {
      await this.validator.repair_order();
    }
    const orderId = ctx.state.orderId;
    const orderInfo = await ctx.service.wallet.rechargeOrder.repairOrder(orderId);
    ctx.logger.info(`${this.logPrefix} repair_order orderId:${orderId} result:${JSON.stringify(orderInfo)}`);
    ctx.body = {
      code: 0,
      desc: '',
      data: orderId,
    };
  }

  public async repair_orders() {
    const { ctx } = this;

    if (typeof this.validator.repair_order === 'function') {
      await this.validator.repair_orders();
    }

    const orderIds = ctx.state.orderIds;

    const fix_ids: any = [];
    for (const orderId of orderIds) {
      try {
        const orderInfo = await ctx.service.wallet.rechargeOrder.repairOrder(orderId);
        ctx.logger.info(`${this.logPrefix} repair_orders orderId:${orderId} result:${JSON.stringify(orderInfo)}`);
        fix_ids.push(orderId);
      } catch (err) {
        ctx.logger.error(`${this.logPrefix} repair_orders fail, order(${orderId})`, err);
      }
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: fix_ids,
    };
  }

  /**
   * 充值
   */
   public async create() {
    const { ctx, config } = this;

    ctx.logger.info(`${this.logPrefix}created recharge order begin.`, ctx.request.body);

    if (typeof this.validator.create === 'function') {
      await this.validator.create();
    }

    // const user: UserEntity = ctx.state.user;
    const form: Record<string, any> = ctx.state.form;
    const wallet: WalletEntity = ctx.state.wallet;
    // 支付商驱动
    const payerUtil = ctx.state.payerUtil;
    const channelConfig: IPayerChannelConfigEx = ctx.state.channelConfig;
    const baseCurrencyType = _.get(ctx.app.config, 'custom.wallet.recharge.exchange_rate_base_currency');

    // --------------------------------
    // 创建充值订单
    // --------------------------------

    const orderTrxData: Record<string, any> = {
      form_order: form,
      exchange_data: {},
    };
    const orderOptions: Record<string, any> = {};

    // 手续费
    let theFee = 0;
    const feeConfig = _.get(channelConfig, 'options.recharge_config.fee');
    if (feeConfig) {
      theFee = payerUtil.computeFees(feeConfig, form.amount);
      theFee = Math.floor(theFee);
    }

    // 订单失效时间
    const orderExpires = await payerUtil.channelRechangeOrderExpires(channelConfig.id);

    // 渠道码
    const channelCode = String(channelConfig.values);

    // 实际订单金额
    // - 对比核算货币与基准货币的汇率差

    // 页面订单金额
    const formAmount = form.amount;
    // 订单基准金额
    let orderAmount = formAmount;

    // 获取渠道的显示货币类型
    const displayCurrentType = _.get(channelConfig, 'display_currency_type') || CurrencyEnum.Usd;
    // 获取渠道的交易货币类型
    const channelCurrentType = _.get(channelConfig, 'currency_type') || CurrencyEnum.Usd;
    let exchangeRates;
    if (displayCurrentType !== baseCurrencyType || baseCurrencyType !== channelCurrentType) {
      exchangeRates = await ctx.service.wallet.currencyExchangeRate.getTodayCurrencyExchangeRate(baseCurrencyType, _.uniq([
        baseCurrencyType,
        displayCurrentType,
        channelCurrentType,
      ]));
      ctx.state.exchangeRates = exchangeRates;
    }

    // 处理外部传入的货币单位跟基准货币单位的金额汇率转换
    if (displayCurrentType !== baseCurrencyType) {
      // if (_.isEmpty(exchangeRates)) {
      //   if (displayCurrentType === CurrencyEnum.Cny && baseCurrencyType === CurrencyEnum.Cny) {
      //     exchangeRates = 7;
      //   } else {
      //     throw new ExError('WAT_WALLET_PAY_CHANNEL_MAINTENANCE', 'cannot find the displayCurrentType => baseCurrencyType exchangeRates', exchangeRates);
      //   }
      // }
      const baseAmount = ctx.service.wallet.currencyExchangeRate.currencyExchangeConvert(orderAmount, exchangeRates, displayCurrentType, baseCurrencyType);
      orderTrxData.exchange_data.base_currency = baseCurrencyType;
      orderTrxData.exchange_data.base_currency_rate = ctx.service.wallet.currencyExchangeRate.getCurrencyRate(exchangeRates, displayCurrentType);
      orderTrxData.exchange_data.base_amount = baseAmount;
      orderAmount = baseAmount;
    }

    // 订单信息
    const rechargeOrderForm: RechargeOrderPartial = {
      status: RechargeOrderStatusEnum.CreateOrderWait,
      wallet_id: wallet._id,
      order_id: orderIdWithRandom(ctx),
      amount: orderAmount,
      handling_fee: theFee,
      payer: channelConfig.payer_id,
      pay_channel: channelCode,
      expire_time: orderExpires,
      callback_url: ctx.request.body.callback_url
      // order_trx_data,
      // options,
    };

    // 生成充值负荷数据
    const payerOrder: Record<string, any> = _.pick(rechargeOrderForm, [
      'amount',
      'order_id',
      'wallet_id',
      'callback_url'
    ]);
    if (baseCurrencyType !== channelCurrentType) {
      if (_.isEmpty(exchangeRates)) {
        if (baseCurrencyType === CurrencyEnum.Cny && channelCurrentType === CurrencyEnum.Cny) {
          exchangeRates = 7;
        } else {
          throw new ExError('WAT_WALLET_PAY_CHANNEL_MAINTENANCE', 'cannot find the baseCurrencyType => channelCurrentType exchangeRates', exchangeRates);
        }
      }
      const channelAmount = ctx.service.wallet.currencyExchangeRate.currencyExchangeConvert(orderAmount, exchangeRates, baseCurrencyType, channelCurrentType);
      orderTrxData.exchange_data.channel_currency = channelCurrentType;
      orderTrxData.exchange_data.channel_currency_rate = ctx.service.wallet.currencyExchangeRate.getCurrencyRate(exchangeRates, channelCurrentType);
      orderTrxData.exchange_data.channel_amount = channelAmount;
      payerOrder.amount = channelAmount;

      // set AmountFloat ExchangeConvert
      const amountFloat: AmountFloat = _.get(channelConfig, 'options.recharge_config.amount_float');
      if (amountFloat) {
        const floatMinAmount = ctx.service.wallet.currencyExchangeRate.currencyExchangeConvert(amountFloat.min_amount, exchangeRates, baseCurrencyType, channelCurrentType);
        const floatMaxAmount = ctx.service.wallet.currencyExchangeRate.currencyExchangeConvert(amountFloat.max_amount, exchangeRates, baseCurrencyType, channelCurrentType);
        _.set(channelConfig, 'options.recharge_config.amount_float.min_amount', floatMinAmount);
        _.set(channelConfig, 'options.recharge_config.amount_float.max_amount', floatMaxAmount);
      }
    }
    payerOrder.order_trx_data = orderTrxData;
    payerOrder.session_id = form.session_id;
    const options = {
      user_id: _.get(ctx.state, 'user._id'),
      email: _.get(ctx.state, 'user.patbg_detail.email'),
      reg_date: _.get(ctx.state, 'user.created_at'),
      trans_link: _.get(ctx.state, 'user.trans_link'),
      withdraw_order_id: form.withdraw_order_id
    };

    const payData = await payerUtil.rechargePayload(payerOrder, channelConfig, options);
    orderTrxData.channel_order = payData;
    // ctx.logger.debug(`${this.logPrefix}theOrder`, payerOrder, exchangeRateCurrency);

    orderOptions.withdraw_order_id = form.withdraw_order_id;
    orderOptions.order_type = form.order_type;
    orderOptions.activity_id = form.activity_id;
    if (form.discount_id) orderOptions.discount_id = form.discount_id;

    // 创建充值订单
    rechargeOrderForm.order_trx_data = orderTrxData;
    rechargeOrderForm.options = orderOptions;
    // if (rechargeOrderForm) {
    //   ctx.logger.debug(`${this.logPrefix}rechargeOrderForm`, rechargeOrderForm);
    //   throw new Error('debug - RechargeOrder');
    // }
    const platform = _.get(ctx.app.config, 'pkg.platform');
    if (platform && platform === 'airmart') {
      _.merge(rechargeOrderForm, {
        extends: {
          client_type: ctx.get('client_type') || '',
        },
      });
    }
    const rechargeOrder: RechargeOrderEntity = await retryTask(async () => {
      return await ctx.service.wallet.rechargeOrder.createRechargeOrder(rechargeOrderForm);
    }, 3, 250);
    if (!rechargeOrder) {
      ctx.logger.error(`${this.logPrefix}createRechargeOrder failed.`, rechargeOrderForm);
      throw new ExError('WAT_DATA_CREATE_FAIL', 'create order failed.');
    }

    // 发起支付订单请求
    const orderReceipt = await payerUtil.recharge(payData, channelConfig, config.env);

    // 订单信息(前台)
    const orderInfo = payerUtil.rechargeOrderInfo(rechargeOrder, orderReceipt, {
      channel_id: channelConfig.id,
    });
    rechargeOrder.order_trx_data.order_info = orderInfo;

    const status = orderInfo.status;
    switch (status) {
      case RechargeOrderStatusEnum.PayCbDone: {
        await ctx.service.wallet.rechargeOrder.orderRechargeSuccess(rechargeOrder, orderReceipt);
        break;
      }
      case RechargeOrderStatusEnum.Expired: {
        await ctx.service.wallet.rechargeOrder.orderRechargeFailed(rechargeOrder, orderReceipt, orderInfo.failMessage);
        break;
      }
      default: {
        await ctx.service.wallet.rechargeOrder.orderRechargePending(rechargeOrder, orderReceipt);
      }
    }

    let rspData: Record<string, any>;
    rspData = _.pick(rechargeOrder, [
      '_id', 'status', 'order_id', 'amount', 'expire_time', 'payer', 'pay_channel', 'created_at',
    ]);
    rspData.order_info = orderInfo;

    // 统计充值拉起次数
    ctx.service.wallet.rechargeOrder.statPayerRechargeSubmit(rechargeOrder);

    ctx.logger.info(`${this.logPrefix}created recharge order done.`);

    ctx.body = {
      code: 0,
      desc: '',
      data: rspData,
    };
  }

  public async spdb_notify() {
    const { ctx } = this;
    const body =  ctx.request.body;

    const fail_ids: any = [];
    for (const orderId of body?.orderIds) {
      try {
        await ctx.service.wallet.rechargeOrder.deliverOrder(orderId);
        sleep(1000);
      } catch (err) {
        ctx.logger.error(`${this.logPrefix} rechargeOrder deliverOrder fail, order(${orderId})`, err);
        fail_ids.push(orderId);
      }
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: fail_ids,
    };
  }

  public async check_spdb_order() {
    const { ctx } = this;

    const allowSearchFields: SearchField[] = [
      {
        key: 'user_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.OBJECT_ID,
      },
      {
        key: 'created_at',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.GT,
          UrlParseSearchOp.GTE,
          UrlParseSearchOp.LT,
          UrlParseSearchOp.LTE,
        ],
        type: SearchTypeEnum.DATE,
      },
      {
        key: 'order_id',
        allow_ops: [
          UrlParseSearchOp.EQ,
          UrlParseSearchOp.IN,
        ],
        type: SearchTypeEnum.STRING,
      },
    ];
    const allowSortFields = [
      '_id',
    ];
    const allowDisplayFields = [
      'status',
      'wallet_id',
      'order_id',
      'amount',
      'created_at',
    ];


    const queryParse = urlParse(ctx.query, {
      search: {
        allowSearchFields,
      },
      sort: {
        allowSortFields,
      },
    });

    let query: RechargeOrderQuery = {
    };
    // Tips: Object is reference, return value is not necessary
    query = filingQuerysByMongo(query, queryParse);
    const fields = filingFieldsByMongo(allowDisplayFields, queryParse) as string[];

    let sorts = {
      _id: -1,
    };
    sorts = filingSortsByMongo(sorts, queryParse);
    query.status = RechargeOrderStatusEnum.CreateOrderDone;
    query.pay_channel = 'spdbPay';
    const aggregates: any[] = [
      { $match: query },
      {
        $lookup: {
          from: 'users',
          localField: 'wallet_id',
          foreignField: 'wallet_id',
          as: 'users',
        },
      },
      {
        $unwind: {
          path: '$wallets',
          preserveNullAndEmptyArrays: true, // 防止未匹配数据导致丢失
        },
      },
      {
        $project: filingFieldsByArray({
          user_id: '$users._id',
          nickname: '$users.patbg_detail.nickname',
          mobile_phone: '$users.patbg_detail.mobile_phone',
        }, fields),
      },
      { $sort: sorts },
    ];

    const countAggregates = [ ...aggregates ];

    const orderCount = await ctx.service.wallet.rechargeOrder.countAggregateRechargeOrder(countAggregates);
    let orders: RechargeOrderEntity[] = [];
    const orderList: any[] = [];
    if (orderCount > 0) {
      const paginatedAggregates = [
        ...aggregates,
      ];
      ctx.logger.info(`${this.logPrefix}paginatedAggregates`, JSON.stringify(paginatedAggregates));
      orders = await ctx.service.wallet.rechargeOrder.aggregateRechargeOrderWithAllowDiskUse(paginatedAggregates);
      const payerConfig = await ctx.service.pay.payer.queryPayerById(3007);
      ctx.state.payerConfig = payerConfig;
      const payerUtilClass = ctx.service.pay.payer.getPayerUtilByName(payerConfig.name);
      const payerUtil = new payerUtilClass({
        ctx,
        options: payerConfig,
      });

      for (const order of orders) {
        const param = {
          tranDate: ctx.service.statistics.getDateStr(order.created_at, 'yyyyMMDD'),
          mrchOrdrNo: order.order_id,
        };
        try {
          const paymentsStatusRes = await payerUtil.paymentsStatus(param);
          ctx.logger.info(`${this.logPrefix} check_spdb_order param:${JSON.stringify(param)} res:${JSON.stringify(paymentsStatusRes)}`);
          if (paymentsStatusRes.statusCode === '0000' && paymentsStatusRes.ordrSt === '00') {
            // 00-支付成功
            orderList.push(order);
            ctx.service.morBg.spdb_recharge_err(order.order_id, String(order?.amount), order.user_id?.[0]);
          }
        } catch (e) {
          ctx.logger.error(`${this.logPrefix} check_spdb_order param:${JSON.stringify(param)} err:${e}`);
        }
      }
    }

    ctx.body = {
      code: 0,
      desc: '',
      data: orderList,
    };
  }
}

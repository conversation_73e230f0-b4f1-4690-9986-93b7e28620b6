{"enabled": true, "name": "代码质量分析器", "description": "监听源代码文件变化，分析修改的代码并提供改进建议，包括代码异味、设计模式和最佳实践。专注于可读性、可维护性和性能优化。", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.go", "**/*.ts", "**/*.js", "**/*.py", "**/*.java", "**/*.cpp", "**/*.c", "**/*.h", "**/*.hpp", "**/*.cs", "**/*.php", "**/*.rb", "**/*.rs", "**/*.kt", "**/*.swift", "**/*.scala", "**/*.sql"]}, "then": {"type": "askAgent", "prompt": "请分析刚刚修改的源代码文件，重点关注以下方面：\n\n1. **代码异味检测**：识别重复代码、过长函数、复杂条件语句、不当命名等问题\n2. **设计模式建议**：推荐适合的设计模式来改善代码结构\n3. **最佳实践**：根据语言特性和项目规范提出改进建议\n4. **性能优化**：识别潜在的性能瓶颈和优化机会\n5. **可读性提升**：建议改善代码可读性的方法\n6. **可维护性增强**：提出提高代码可维护性的建议\n\n请用中文回复，提供具体的代码示例和改进方案，确保建议实用且易于实施。保持现有功能不变的前提下进行优化。"}}
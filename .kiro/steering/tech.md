# 技术栈

## Go 服务技术栈

### 核心框架和库
- **Go 1.19** - 主要编程语言
- **Gin** - HTTP Web框架
- **GORM** - ORM数据库操作
- **go-micro v4** - 微服务框架
- **go-redis** - Redis客户端
- **Kafka** - 消息队列 (使用 kafkajs 和 Shopify/sarama)
- **MongoDB** - NoSQL数据库 (使用 go-mongox)
- **Elasticsearch** - 搜索引擎
- **OpenTelemetry** - 链路追踪和监控

### 数据库
- **MySQL** - 主数据库
- **Redis** - 缓存和会话存储
- **MongoDB** - 文档存储
- **Elasticsearch** - 搜索和分析

### 第三方集成
- **腾讯云COS** - 对象存储
- **极光推送** - 消息推送
- **支付宝SDK** - 支付集成

## Node.js 服务技术栈

### 核心框架
- **Egg.js** - Node.js企业级框架
- **TypeScript** - 类型安全的JavaScript
- **Bull** - 队列处理
- **Socket.io** - 实时通信

### 数据库和缓存
- **Mongoose** - MongoDB ODM
- **egg-redis** - Redis集成
- **egg-mysql** - MySQL集成

### 认证和安全
- **Passport.js** - 认证中间件
- **JWT** - JSON Web Token
- **Casbin** - 权限控制

## 常用命令

### Go 服务
```bash
# 代码检查和格式化
make lint-install  # 安装golangci-lint
make lint         # 运行代码检查
make check        # 完整代码检查

# API文档生成
make swag         # 生成Swagger文档

# 错误码检查
make check-error-codes  # 检查错误码重复和范围
make check-all         # 完整检查（代码+错误码）

# 运行服务
go run cmd/http_server/main.go  # HTTP服务
go run cmd/task_server/main.go  # 任务服务
```

### Node.js 服务
```bash
# 开发环境
npm run dev       # 开发模式启动
npm run debug     # 调试模式

# 生产环境
npm run start     # 生产环境启动
npm run stop      # 停止服务

# 不同环境部署
npm run sit       # SIT环境
npm run pre       # 预生产环境
npm run sit-nod   # SIT环境(非守护进程)
npm run pre-nod   # 预生产环境(非守护进程)

# 代码质量
npm run lint      # 代码检查
npm run test      # 运行测试
npm run tsc       # TypeScript编译
```

## 容器化
- **Docker** - 容器化部署
- **docker-compose** - 本地开发环境编排
- 支持多环境配置 (dev/sit/sim/pre/prod)
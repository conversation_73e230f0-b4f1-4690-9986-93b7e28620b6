# 项目结构

## 整体架构

这是一个多服务微服务架构，每个服务都有独立的代码库和部署单元。

## Go 服务结构 (以 app_service 为例)

```
app_service/
├── apps/                           # 业务模块
│   ├── business/                   # 业务相关模块
│   │   ├── [module_name]/         # 具体业务模块
│   │   │   ├── api/               # API层 - 处理HTTP请求
│   │   │   ├── service/           # Service层 - 业务逻辑
│   │   │   ├── repository/        # Repository层 - 数据访问
│   │   │   ├── model/             # 数据模型定义
│   │   │   ├── define/            # 请求/响应结构体
│   │   │   ├── constant/          # 常量定义
│   │   │   ├── task/              # 异步任务
│   │   │   │   ├── msg/           # 消息队列任务
│   │   │   │   └── rpc/           # 定时任务
│   │   │   ├── facade/            # 对外暴露接口
│   │   │   └── router/            # 路由定义
│   │   └── platform/              # 平台相关模块
│   └── init_router.go             # 路由初始化
├── cmd/                           # 应用入口
│   ├── http_server/               # HTTP服务入口
│   └── task_server/               # 任务服务入口
├── conf/                          # 配置文件
│   ├── http_server/               # HTTP服务配置
│   └── task_server/               # 任务服务配置
├── global/                        # 全局变量和配置
├── pkg/                           # 公共包
│   ├── middlewares/               # 中间件
│   ├── util/                      # 工具函数
│   ├── cache/                     # 缓存相关
│   └── pagination/                # 分页工具
├── third_party/                   # 第三方服务集成
├── gen/                           # 代码生成工具
├── docs/                          # API文档
└── deploy/                        # 部署脚本
```

## Node.js 服务结构 (Egg.js)

```
[service_name]/
├── app/                           # 应用代码
│   ├── controller/                # 控制器层
│   ├── service/                   # 服务层
│   ├── model/                     # 数据模型
│   ├── middleware/                # 中间件
│   ├── router.ts                  # 路由定义
│   ├── routes/                    # 路由模块
│   ├── utils/                     # 工具函数
│   ├── validator/                 # 参数验证
│   ├── queue/                     # 队列处理
│   └── schedule/                  # 定时任务
├── config/                        # 配置文件
│   ├── config.default.ts          # 默认配置
│   ├── config.local.ts            # 本地开发配置
│   ├── config.sit.ts              # SIT环境配置
│   ├── config.pre.ts              # 预生产配置
│   ├── config.prod.ts             # 生产环境配置
│   └── plugin.ts                  # 插件配置
├── typings/                       # TypeScript类型定义
│   ├── app/                       # 应用类型
│   ├── config/                    # 配置类型
│   ├── const/                     # 常量类型
│   ├── entity/                    # 实体类型
│   ├── enum/                      # 枚举类型
│   └── interface/                 # 接口类型
├── test/                          # 测试文件
└── logs/                          # 日志文件
```

## 代码组织规范

### Go 服务分层架构
1. **API层** - 处理HTTP请求，参数验证，响应格式化
2. **Service层** - 业务逻辑处理，事务管理
3. **Repository层** - 数据访问，数据库操作
4. **Model层** - 数据结构定义，数据库表映射

### 模块命名规范
- 使用小写字母和下划线
- 模块名应该清晰表达业务含义
- 避免使用缩写，除非是广泛认知的缩写

### 文件命名规范
- Go文件使用小写字母和下划线：`user_service.go`
- TypeScript文件使用小写字母和下划线：`user.service.ts`
- 测试文件添加`_test`后缀：`user_service_test.go`

### 环境配置
支持多环境配置：
- **local/dev** - 本地开发环境
- **sit** - 系统集成测试环境
- **sim** - 仿真环境
- **pre** - 预生产环境
- **prod** - 生产环境

### 公共组件位置
- **pkg/** - Go服务公共包
- **app/utils/** - Node.js服务工具函数
- **global/** - Go服务全局配置和变量
- **typings/** - TypeScript类型定义
# Design Document

## Overview

本设计文档描述了对 `/tmt/admin/v1/welcome` 接口的系统性重构优化方案。主要目标是提升代码质量、性能和可维护性，同时确保功能完整性和数据准确性。

重构将采用分层架构和模块化设计，将复杂的单体方法拆分为职责单一的服务类和工具方法，优化数据库查询性能，并建立完善的错误处理和日志记录机制。

## Architecture

### 当前架构问题
- 单个controller方法过于庞大（831行）
- 业务逻辑、数据访问、数据处理混合在一起
- 大量重复的数据库查询逻辑
- 缺乏适当的错误处理和日志记录

### 目标架构设计

基于项目中现有的报表实现模式（如passport_bg的system_daily接口），采用以下架构：

```
Controller Layer (路由层)
├── WelcomeController
│   └── panel() - 主入口方法，协调各个服务
│
Service Layer (业务服务层)
├── WelcomePanelService - 面板数据聚合服务（类似SystemDaily）
├── UserStatisticsService - 用户维度统计服务
├── OrderStatisticsService - 订单维度统计服务
├── IssueItemStatisticsService - 首发商品统计服务
└── SaleOrderStatisticsService - 二次流转统计服务
│
Report Service Layer (报表服务层) - 复用现有模式
├── ReportUser - 用户报表数据（已存在）
├── ReportIssueItem - 首发商品报表数据（已存在）
├── ReportSaleOrder - 二次流转报表数据（已存在）
└── ReportOverallBizSummary - 业务汇总报表（已存在）
│
Utility Layer (工具层)
├── TimeRangeUtil - 时间范围处理工具
├── DatabaseQueryUtil - 数据库查询优化工具
└── StatisticsCalculator - 统计计算工具
│
External Service Layer (外部服务层)
├── PatBgService - passport_bg服务调用
└── WatBgService - wallet_bg服务调用
```

### 现有报表架构分析

项目中已有完善的报表架构：
1. **Report Service模式**: 使用MongoDataModel基类，提供标准的CRUD操作
2. **归档机制**: ReportOverallBizSummary.archiveTodayData()定时归档数据
3. **Redis缓存**: passport_bg使用Redis pipeline批量获取缓存数据
4. **分类查询**: 通过DailyTypeEnum区分不同类型的统计数据

## Components and Interfaces

### 1. WelcomePanelService (主服务类)

```typescript
interface WelcomePanelService {
  /**
   * 获取面板数据
   * @param params 查询参数
   * @returns 面板数据
   */
  getPanelData(params: PanelQueryParams): Promise<PanelDataResponse>;
  
  /**
   * 获取单日面板数据
   * @param timeRange 时间范围
   * @param filters 过滤条件
   * @returns 单日面板数据
   */
  getOneDayPanelData(timeRange: TimeRange, filters: DataFilters): Promise<PanelDataEntity>;
}

interface PanelQueryParams {
  item_id?: ObjectID;
  sku_no?: string;
  start_time: string;
  end_time: string;
}

interface PanelDataResponse {
  code: number;
  desc: string;
  data: {
    totalPanelData: Record<string, PanelDataEntity>;
  };
}
```

### 2. 统计服务接口

```typescript
interface UserStatisticsService {
  getUserStatistics(timeRange: TimeRange): Promise<UserInfo>;
}

interface OrderStatisticsService {
  getOrderStatistics(timeRange: TimeRange): Promise<OrderInfo>;
}

interface IssueItemStatisticsService {
  getIssueItemStatistics(timeRange: TimeRange, filters: ItemFilters): Promise<IssueItemOrder>;
}

interface SaleOrderStatisticsService {
  getSaleOrderStatistics(timeRange: TimeRange, filters: ItemFilters): Promise<SaleOrders>;
}
```

### 3. 数据访问层接口

```typescript
interface UserDataRepository {
  getRegisteredUsers(timeRange: TimeRange): Promise<User[]>;
  getLoginUsers(timeRange: TimeRange): Promise<User[]>;
  getCertifiedUsers(): Promise<User[]>;
  getTransactionUsers(timeRange: TimeRange): Promise<string[]>;
}

interface OrderDataRepository {
  getIssueItemOrders(query: OrderQuery): Promise<IssueItemOrderData[]>;
  getWithdrawOrders(query: OrderQuery): Promise<WithdrawOrderData[]>;
  aggregateOrderData(pipeline: any[]): Promise<any[]>;
}
```

## Data Models

### 核心数据模型

```typescript
interface TimeRange {
  start: Date;
  end: Date;
}

interface DataFilters {
  item_id?: ObjectID;
  sku_no?: string;
}

interface PanelDataEntity {
  user: UserInfo;
  orderInfo: OrderInfo;
  issueItemOrder: IssueItemOrder;
  saleOrders: SaleOrders;
}

interface UserInfo {
  registeredUserCount: number;    // 注册人数
  loginCount: number;             // 登录人数
  transactionUserCount: number;   // 交易成功人数
  rechargeUserCount: number;      // 充值人数
  drawUserCount: number;          // 提现人数
  certifiedCount: number;         // 实名人数
}

interface OrderInfo {
  rechargeAmount: number;         // 充值金额
  orderTotalAmount: number;       // 交易流水
  drawAmount: number;             // 提现金额
  averageTotalOrderPrice: number; // 交易客单价
  averageRechargePrice: number;   // 充值客单价
  DrawPriceFee: number;           // 提现手续费
}
```

## Error Handling

### 错误处理策略

1. **分层错误处理**
   - Controller层：捕获所有异常，返回统一格式的错误响应
   - Service层：处理业务逻辑异常，记录详细错误日志
   - Repository层：处理数据访问异常，提供数据库错误信息

2. **错误类型定义**
```typescript
enum WelcomeApiErrorCode {
  INVALID_PARAMS = 'INVALID_PARAMS',
  DATABASE_ERROR = 'DATABASE_ERROR',
  CALCULATION_ERROR = 'CALCULATION_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR'
}

interface ApiError {
  code: WelcomeApiErrorCode;
  message: string;
  details?: any;
  timestamp: Date;
}
```

3. **错误处理实现**
```typescript
class WelcomeApiError extends Error {
  constructor(
    public code: WelcomeApiErrorCode,
    public message: string,
    public details?: any
  ) {
    super(message);
  }
}

// 统一错误处理装饰器
function handleErrors(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value;
  descriptor.value = async function (...args: any[]) {
    try {
      return await method.apply(this, args);
    } catch (error) {
      this.ctx.logger.error(`[${target.constructor.name}.${propertyName}] Error:`, error);
      throw new WelcomeApiError(
        WelcomeApiErrorCode.CALCULATION_ERROR,
        '数据计算异常',
        error
      );
    }
  };
}
```

## Testing Strategy

### 测试层次

1. **单元测试**
   - 每个Service类的独立测试
   - 工具类和计算方法的测试
   - Mock外部依赖（数据库、第三方服务）

2. **集成测试**
   - Controller与Service的集成测试
   - 数据库查询的集成测试
   - 完整业务流程的端到端测试

3. **性能测试**
   - 数据库查询性能测试
   - 接口响应时间测试
   - 并发访问压力测试

### 测试数据准备

```typescript
interface TestDataBuilder {
  createUserTestData(count: number): Promise<User[]>;
  createOrderTestData(count: number): Promise<Order[]>;
  createItemTestData(count: number): Promise<Item[]>;
  cleanupTestData(): Promise<void>;
}
```

### 测试用例设计

1. **正常流程测试**
   - 标准时间范围查询
   - 带筛选条件的查询
   - 跨天数据查询

2. **边界条件测试**
   - 空数据情况
   - 极大时间范围
   - 无效参数处理

3. **异常情况测试**
   - 数据库连接异常
   - 第三方服务异常
   - 数据不一致情况

## Performance Optimization

### 当前性能瓶颈分析

基于实际测试数据，7天查询场景下的性能瓶颈如下：

#### 主要瓶颈点
1. **串行按天查询**: `panel`函数按天顺序串行调用`getOneDayPanel`，造成N（天数）×4次I/O
   - 优化前：7天查询需要4秒
   - 优化后：7天查询2秒以内，降低50%

2. **外部服务调用延迟**:
   - `patbg/get_users_registered_count`: 53ms
   - `patbg/get_users_login_count`: 121ms

3. **数据库查询延迟**:
   - `report_issue_item`表查询: 93ms
   - `report_sale_order`表查询: 87ms
   - `issue_item`表查询: 179ms
   - `sale_order`表查询: 64ms

#### 索引优化问题
- 默认使用`sort: {"_id": -1}`导致无法命中日期索引
- 需要改为`sort: {"date": -1}`来命中`date_-1`索引

### 数据库查询优化

1. **已实施的索引优化**
```sql
-- wc-tmtbgdb 数据库
db.report_issue_item.createIndex({ "date": -1 }, { background: true })
db.report_sale_order.createIndex({ "date": -1 }, { background: true }) 
db.report_user.createIndex({ "date": -1 }, { background: true })
db.issue_item.createIndex({ "status": 1 })

-- wc-patbg 数据库
db.users.createIndex({ "created_at": -1 }, { background: true })
db.user_login_records.createIndex({ "login_time": -1 }, { background: true })
```

2. **查询优化策略**

   **方案1: 并行化单日计算**
   ```typescript
   // 将串行改为并行执行
   const dayPromises = dayList.map(range => 
     this.getOneDayPanel(range.start, range.end, item_id, sku_no)
   );
   const dayResults = await Promise.all(dayPromises);
   ```
   - 适用于天数较少（<30）场景
   - 需要限制并发度避免压垮数据库

   **方案2: 批量聚合查询**
   ```typescript
   // 通过$group:{_id:{day:...}}实现一次查询多天数据
   const batchAggregation = [
     {
       $match: { created_at: { $gte: start, $lte: end } }
     },
     {
       $group: {
         _id: { 
           day: { $dateToString: { format: "%Y-%m-%d", date: "$created_at" } }
         },
         count: { $sum: 1 }
       }
     }
   ];
   ```
   - I/O次数由N×4→4，理论最优
   - 适合查询天数较多或高并发场景

   **方案3: 预聚合/定时离线化（基于现有Report架构）**
   ```typescript
   // 扩展现有的ReportOverallBizSummary，增加welcome面板数据
   interface WelcomePanelSnapshot {
     date: Date;
     user: UserInfo;
     orderInfo: OrderInfo;
     issueItemOrder: IssueItemOrder;
     saleOrders: SaleOrders;
   }
   
   // 类似ReportOverallBizSummary.archiveTodayData的实现
   public async archiveWelcomePanelData(start_time: string, end_time: string) {
     // 计算各维度统计数据
     // 存储到report_welcome_panel集合
   }
   ```
   - 复用现有的Report Service架构
   - 实时接口只读快照，响应极快
   - 适用于对实时性要求≤1天的场景

3. **缓存策略（基于现有Redis架构）**
```typescript
// 参考passport_bg的SystemDaily实现
interface WelcomeCacheStrategy {
  // Redis Pipeline批量操作（类似SystemDaily.querySystemDailyByDataRange）
  getWelcomePanelDataByRange(dateRange: Date[]): Promise<Record<string, PanelDataEntity>>;
  
  // 缓存键生成（类似getSystemDailyKey）
  getWelcomePanelKey(date: moment.Moment): string;
  
  // 批量缓存设置
  setWelcomePanelCache(date: Date, data: PanelDataEntity): Promise<void>;
  
  // 分层缓存策略
  // L1: Redis Hash存储（类似SystemDaily） - 5-30分钟
  // L2: MongoDB Report集合 - 永久存储
  // L3: 内存缓存 - 1分钟热点数据
}

// Redis键命名规范（参考现有模式）
const CACHE_KEYS = {
  WELCOME_PANEL: (date: string) => `welcome:panel:${date}`,
  WELCOME_USER: (date: string) => `welcome:user:${date}`,
  WELCOME_ORDER: (date: string) => `welcome:order:${date}`,
  WELCOME_ITEM: (date: string) => `welcome:item:${date}`,
  WELCOME_SALE: (date: string) => `welcome:sale:${date}`
};
```

### 代码性能优化

1. **查询排序优化**
   ```typescript
   // 修改默认排序，确保命中索引
   const queryOptions = {
     sort: { date: -1 }  // 替代默认的 { _id: -1 }
   };
   ```

2. **日志优化**
   ```typescript
   // 减少JSON.stringify日志量，改为结构化日志
   // 优化前
   ctx.logger.info('入参：', JSON.stringify({item_id,sku_no,start_time,end_time}));
   
   // 优化后
   ctx.logger.info('入参', { item_id, sku_no, start_time, end_time });
   ```

3. **时间处理优化**
   ```typescript
   // splitTimeRangeByDay直接返回moment对象，避免多次new Date
   // 封装ctx.service.statistics.setMomentUtcOffset为纯函数并缓存时区偏移
   ```

4. **批量查询优化**
   ```typescript
   // 一次同表查多个数据，多天查询改为批量查询
   // 循环单个查询改成一次查询多个
   const batchQuery = {
     date: { 
       $in: dayList.map(day => day.date)  // 批量查询多天数据
     }
   };
   ```

5. **连接池优化**
   - 异步并行使用Promise.all，但受连接池限制
   - 连接池大小由数据库服务器CPU核心数和客户端数量限制
   - 优化效果有限，需要配合其他策略

### 后期优化需求

1. **业务需求调整**
   - 拆分成多个页面查询
   - 按挂牌编码、SKU编码查询时，仅统计首发版块和流转版块、提货的数据

2. **数据存储优化**
   - 数据库存储时间只精确到天：range优化成eq查询
   - 当天数据实时查询，历史数据使用定时离线化

3. **综合优化建议**
   - 若报表天数通常≤30且要求分钟级实时：采用方案1（并行化）+ 细节优化
   - 若经常拉取30天以上且并发大：优先方案2（批量聚合）
   - 若对实时性要求不高：方案3（预聚合）最具性价比，可与缓存组合

## Implementation Plan

### 重构步骤

1. **第一阶段：基础性能优化**
   - 实施索引优化（已完成）
   - 修改查询排序策略，确保命中索引
   - 实现并行化单日计算
   - 优化日志和时间处理
   - 实现批量查询替代循环查询

2. **第二阶段：架构重构**
   - 创建Service层基础结构
   - 提取工具类和公共方法
   - 建立错误处理机制
   - 实现分层缓存策略

3. **第三阶段：业务逻辑迁移**
   - 迁移用户统计逻辑到UserStatisticsService
   - 迁移订单统计逻辑到OrderStatisticsService
   - 迁移商品统计逻辑到相应Service
   - 实现批量聚合查询方案

4. **第四阶段：高级优化**
   - 实现预聚合/定时离线化
   - 添加性能监控和告警
   - 实现数据存储时间精确到天的优化
   - 支持业务需求的页面拆分

5. **第五阶段：测试和验证**
   - 编写单元测试和集成测试
   - 性能对比验证（目标：保持2秒以内响应）
   - 数据准确性验证

### 风险控制

1. **数据一致性保证**
   - 重构过程中保持原有计算逻辑不变
   - 通过对比测试验证数据准确性
   - 分步骤迁移，每步都进行验证

2. **向后兼容性**
   - 保持接口签名不变
   - 保持返回数据格式不变
   - 渐进式重构，避免大爆炸式改动

3. **回滚策略**
   - 保留原有代码作为备份
   - 支持快速回滚到原实现
   - 建立监控告警机制